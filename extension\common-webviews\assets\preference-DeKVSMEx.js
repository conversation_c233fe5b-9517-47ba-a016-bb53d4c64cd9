import{B as b,K as h,O as J,t as o,Q as a,Y as F,_ as gt,a5 as W,ab as A,b as p,Z as et,a7 as ft,A as dt,aa as yt,m,al as wt,aE as pt,H as t,D as c,C as X,G as tt,I as kt,J as mt,P as At,S as ht,N as bt,am as $t,V as Bt,aB as qt}from"./SpinnerAugment-kH3m-zOb.js";import{c as q,W as G}from"./IconButtonAugment-C8Qb_O9b.js";import{aJ as lt}from"./AugmentMessage-CsMcb4z_.js";import{b as Ct,a as xt}from"./BaseTextInput-TeF8u93x.js";import{C as Mt,S as Rt}from"./folder-opened-D9klsFkp.js";import{M as St}from"./message-broker-CwcPcQ_e.js";import{s as It}from"./chat-context-nrEZUHNl.js";import{M as Dt}from"./index-B_qLJJaQ.js";import"./CalloutAugment-BUpCn_TI.js";import"./CardAugment-BAmr_-U4.js";import"./index-BnlWKkvq.js";import"./async-messaging-CCLqHBoR.js";import"./types-CGlLNakm.js";import"./file-paths-Bl_IgYJd.js";import"./index-D-fDrvnq.js";import"./diff-operations-DJ1OAK7V.js";import"./svelte-component-B6upIdsM.js";import"./Filespan-B9x3U15q.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./keypress-DD1aQVr0.js";import"./await-BH7XNauH.js";import"./OpenFileButton-CgBtRJfx.js";import"./index-C4gKbsWy.js";import"./remote-agents-client-TVS4i5h_.js";import"./ra-diff-ops-model-dZccOtNT.js";import"./TextAreaAugment-IsRYkfgU.js";import"./ButtonAugment-BZfc2Zk1.js";import"./CollapseButtonAugment-DNisU6cu.js";import"./user-DigCn7eq.js";import"./MaterialIcon-Cs9y5rDt.js";import"./CopyButton-BDqSrh0t.js";import"./ellipsis-2hnmWCsv.js";import"./IconFilePath-C18xEJF3.js";import"./LanguageIcon-Bn3MfWRN.js";import"./next-edit-types-904A5ehg.js";import"./chevron-down-BbSBSK7f.js";import"./index-XEt2J8A6.js";import"./augment-logo-C7lllHv9.js";import"./pen-to-square-CYPFKWo4.js";import"./check-C8b2LRem.js";var Wt=h('<div class="header svelte-1894wv4"> </div>'),_t=h('<div class="container svelte-1894wv4"><!> <div class="buttons svelte-1894wv4"><button type="button">A</button> <button type="button">A</button> <button type="button">A</button> <button type="button">=</button> <button type="button">B</button> <button type="button">B</button> <button type="button">B</button></div></div>');function nt(L,C){let i=b(C,"selected",12,null),x=b(C,"question",8,null);function v(w){i(w)}var e=_t(),d=o(e),_=w=>{var P=Wt(),Z=o(P);F(()=>et(Z,x())),p(w,P)};J(d,w=>{x()&&w(_)});var M=a(d,2),g=o(M);let f;var r=a(g,2);let $;var y=a(r,2);let s;var R=a(y,2);let U;var E=a(R,2);let V;var O=a(E,2);let N;var Q=a(O,2);let Y;F((w,P,Z,at,st,l,u)=>{f=W(g,1,"button large svelte-1894wv4",null,f,w),$=W(r,1,"button medium svelte-1894wv4",null,$,P),s=W(y,1,"button small svelte-1894wv4",null,s,Z),U=W(R,1,"button equal svelte-1894wv4",null,U,at),V=W(E,1,"button small svelte-1894wv4",null,V,st),N=W(O,1,"button medium svelte-1894wv4",null,N,l),Y=W(Q,1,"button large svelte-1894wv4",null,Y,u)},[()=>({highlighted:i()==="A3"}),()=>({highlighted:i()==="A2"}),()=>({highlighted:i()==="A1"}),()=>({highlighted:i()==="="}),()=>({highlighted:i()==="B1"}),()=>({highlighted:i()==="B2"}),()=>({highlighted:i()==="B3"})],gt),A("click",g,()=>v("A3")),A("click",r,()=>v("A2")),A("click",y,()=>v("A1")),A("click",R,()=>v("=")),A("click",E,()=>v("B1")),A("click",O,()=>v("B2")),A("click",Q,()=>v("B3")),p(L,e)}var Et=h('<div class="question svelte-1i0f73l"> </div>'),Ot=h('<div class="container svelte-1i0f73l"><!> <textarea class="input svelte-1i0f73l" rows="3"></textarea></div>'),Pt=h('<button class="button svelte-2k5n"> </button>'),Ht=h("<div> </div>"),zt=h('<div class="container svelte-n0uy88"><!> <label class="custom-checkbox svelte-n0uy88"><input type="checkbox" class="svelte-n0uy88"/> <span class="svelte-n0uy88"></span></label></div>'),Ft=h("<!> <!> <!> <!> <!> <!>",1),Gt=h("<p>Streaming in progress... Please wait for both responses to complete.</p>"),Jt=h('<main><div class="l-pref svelte-751nif"><h1 class="svelte-751nif">Input message</h1> <!> <hr class="l-side-by-side svelte-751nif"/> <div class="l-side-by-side svelte-751nif"><div class="l-side-by-side__child svelte-751nif"><h1 class="svelte-751nif">Option A</h1> <!></div> <div class="divider svelte-751nif"></div> <div class="l-side-by-side__child svelte-751nif"><h1 class="svelte-751nif">Option B</h1> <!></div></div> <hr class="svelte-751nif"/> <!></div></main>');function Lt(L,C){dt(C,!1);const i=m(),x=m(),v=m();let e=b(C,"inputData",8);const d=yt();let _=new Mt(new St(q),q,new Rt);It(_);let M=m(null),g=m(null),f=null,r=m(null),$=m(""),y=m(!1),s=m({a:null,b:null}),R=m(e().data.a.response.length>0&&e().data.b.response.length>0);function U(){if(f="=",t(r)===null)return void d("notify","Overall rating is required");const l={overallRating:t(r),formattingRating:t(M)||"=",hallucinationRating:f||"=",instructionFollowingRating:t(g)||"=",isHighQuality:t(y),textFeedback:t($)};d("result",l)}wt(()=>{window.addEventListener("message",l=>{const u=l.data;u.type===G.chatModelReply?(u.stream==="A"?pt(s,t(s).a=u.data.text):u.stream==="B"&&pt(s,t(s).b=u.data.text),c(s,t(s))):u.type===G.chatStreamDone&&c(R,!0)})}),X(()=>t(r),()=>{var l;c(i,(l=t(r))==="="||l===null?"Is this a high quality comparison?":`Are you completely happy with response '${l.startsWith("A")?"A":"B"}'?`)}),X(()=>(t(s),tt(e())),()=>{c(x,t(s).a!==null?t(s).a:e().data.a.response)}),X(()=>(t(s),tt(e())),()=>{c(v,t(s).b!==null?t(s).b:e().data.b.response)}),X(()=>tt(e()),()=>{c(R,e().data.a.response.length>0&&e().data.b.response.length>0)}),kt(),mt();var E=Jt(),V=o(E),O=a(o(V),2);lt(O,{get markdown(){return tt(e()),At(()=>e().data.a.message)}});var N=a(O,4),Q=o(N),Y=a(o(Q),2);lt(Y,{get markdown(){return t(x)}});var w=a(Q,4),P=a(o(w),2);lt(P,{get markdown(){return t(v)}});var Z=a(N,4),at=l=>{var u=Ft(),ot=bt(u);nt(ot,{question:"Which response is formatted better? (e.g. level of detail style, structure)?",get selected(){return t(M)},set selected(n){c(M,n)},$$legacy:!0});var rt=a(ot,2);nt(rt,{question:"Which response follows your instruction better?",get selected(){return t(g)},set selected(n){c(g,n)},$$legacy:!0});var ut=a(rt,2);nt(ut,{question:"Which response is better overall?",get selected(){return t(r)},set selected(n){c(r,n)},$$legacy:!0});var vt=a(ut,2);(function(n,k){let j=b(k,"isChecked",12,!1),S=b(k,"question",8,null);var B=zt(),I=o(B),H=D=>{var K=Ht(),it=o(K);F(()=>et(it,S())),p(D,K)};J(I,D=>{S()&&D(H)});var z=a(I,2),T=o(z);xt(T,j),p(n,B)})(vt,{get question(){return t(i)},get isChecked(){return t(y)},set isChecked(n){c(y,n)},$$legacy:!0});var ct=a(vt,2);(function(n,k){let j=b(k,"value",12,""),S=b(k,"question",8,null),B=b(k,"placeholder",8,"");var I=Ot(),H=o(I),z=D=>{var K=Et(),it=o(K);F(()=>et(it,S())),p(D,K)};J(H,D=>{S()&&D(z)});var T=a(H,2);F(()=>ft(T,"placeholder",B())),Ct(T,j),p(n,I)})(ct,{question:"Any additional feedback?",placeholder:"Please explain your answers to the above questions.",get value(){return t($)},set value(n){c($,n)},$$legacy:!0}),function(n,k){let j=b(k,"label",8,"Submit"),S=b(k,"onClick",8);var B=Pt(),I=o(B);F(()=>et(I,j())),A("click",B,function(...H){var z;(z=S())==null||z.apply(this,H)}),p(n,B)}(a(ct,2),{label:"Submit",onClick:U}),p(l,u)},st=l=>{var u=Gt();p(l,u)};J(Z,l=>{t(R)?l(at):l(st,!1)}),p(L,E),ht()}var Nt=h("<main><!></main>");function Qt(L,C){dt(C,!1);let i=m();function x(e){const d=e.detail;q.postMessage({type:G.preferenceResultMessage,data:d})}function v(e){q.postMessage({type:G.preferenceNotify,data:e.detail})}q.postMessage({type:G.preferencePanelLoaded}),mt(),A("message",$t,function(e){const d=e.data;d.type===G.preferenceInit&&c(i,d.data)}),Dt.Root(L,{children:(e,d)=>{var _=Nt(),M=o(_),g=f=>{var r=Bt(),$=bt(r),y=s=>{Lt(s,{get inputData(){return t(i)},$$events:{result:x,notify:v}})};J($,s=>{t(i).type==="Chat"&&s(y)}),p(f,r)};J(M,f=>{t(i)&&f(g)}),p(e,_)},$$slots:{default:!0}}),ht()}(async function(){q&&q.initialize&&await q.initialize(),qt(Qt,{target:document.getElementById("app")})})();
