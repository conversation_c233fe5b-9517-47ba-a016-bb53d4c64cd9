import{A as ee,B as H,C as Q,D as v,m as h,G as k,H as e,I as ae,J as se,V as te,N as A,O as G,P as f,b as a,S as le,W as oe,X as y,K as E,Y as U,Z as W,t as re,Q as Y,_ as ie,$ as ne,a0 as de}from"./SpinnerAugment-kH3m-zOb.js";import{e as ce,i as ue}from"./IconButtonAugment-C8Qb_O9b.js";import{A as ve,D as d}from"./index-BnlWKkvq.js";import{B as J}from"./ButtonAugment-BZfc2Zk1.js";import{C as he}from"./chevron-down-BbSBSK7f.js";import{T as fe}from"./CardAugment-BAmr_-U4.js";import{R}from"./message-broker-CwcPcQ_e.js";var pe=E('<div class="c-dropdown-label svelte-9n7h82"><!></div>'),me=E("<!> <!>",1),$e=E("<!> <!>",1),ge=E("<!> <!>",1);function Se(K,T){ee(T,!1);const[q,O]=oe(),p=()=>ne(e(C),"$focusedIndex",q),w=h(),S=h(),s=h();let P=H(T,"onSave",8),i=H(T,"rule",8);const x=[{label:"Always",value:R.ALWAYS_ATTACHED,description:"These Rules will be included in every message you send to the agent."},{label:"Manual",value:R.MANUAL,description:"These Rules will be included when manually tagged in your message. You can tag Rules by @-mentioning them."},{label:"Auto",value:R.AGENT_REQUESTED,description:"These Rules will be included when the Agent decides to fetch them based on this file's description."}];let C=h(void 0),I=h(()=>{});Q(()=>k(i()),()=>{v(w,i().path)}),Q(()=>k(i()),()=>{v(S,i().type)}),Q(()=>e(S),()=>{v(s,x.find(t=>t.value===e(S)))}),ae(),se();var z=te(),V=A(z),X=t=>{fe(t,{content:"Workspace guidelines are always applied",children:(r,j)=>{J(r,{color:"accent",size:1,disabled:!0,children:(m,D)=>{var N=y("Always");a(m,N)},$$slots:{default:!0}})},$$slots:{default:!0}})},Z=t=>{d.Root(t,{get requestClose(){return e(I)},set requestClose(r){v(I,r)},get focusedIndex(){return e(C)},set focusedIndex(r){de(v(C,r),"$focusedIndex",q)},children:(r,j)=>{var m=ge(),D=A(m);d.Trigger(D,{children:(_,F)=>{var c=pe(),$=re(c);J($,{color:"neutral",size:1,variant:"soft",children:(u,L)=>{var l=y();U(()=>W(l,(e(s),f(()=>e(s).label)))),a(u,l)},$$slots:{default:!0,iconRight:(u,L)=>{he(u,{slot:"iconRight"})}}}),a(_,c)},$$slots:{default:!0}});var N=Y(D,2);d.Content(N,{side:"bottom",align:"start",children:(_,F)=>{var c=$e(),$=A(c);ce($,1,()=>x,ue,(l,o)=>{const g=ie(()=>(e(s),e(o),f(()=>e(s).label===e(o).label)));d.Item(l,{onSelect:()=>async function(n){e(I)();try{await P()(n.value,n.value!==R.AGENT_REQUESTED||i().description?i().description:"Example description")}catch(b){console.error("RulesModeSelector: Error in onSave:",b)}}(e(o)),get highlight(){return e(g)},children:(n,b)=>{var M=y();U(()=>W(M,(e(o),f(()=>e(o).label)))),a(n,M)},$$slots:{default:!0}})});var u=Y($,2),L=l=>{var o=me(),g=A(o);d.Separator(g,{});var n=Y(g,2);d.Label(n,{children:(b,M)=>{var B=y();U(()=>W(B,(p(),e(s),f(()=>p()!==void 0?x[p()].description:e(s).description)))),a(b,B)},$$slots:{default:!0}}),a(l,o)};G(u,l=>{(p()!==void 0||e(s))&&l(L)}),a(_,c)},$$slots:{default:!0}}),a(r,m)},$$slots:{default:!0},$$legacy:!0})};G(V,t=>{e(w),f(()=>e(w)===ve)?t(X):t(Z,!1)}),a(K,z),le(),O()}export{Se as R};
