@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Augment扩展系统平台信息修改脚本 (Windows批处理版本)
:: 用于干扰扩展的系统平台信息检查，避免多账号检测

title Augment扩展平台信息修改工具

:: 设置路径
set "SCRIPT_DIR=%~dp0"
set "EXTENSION_DIR=%SCRIPT_DIR%extension\out"
set "EXTENSION_JS=%EXTENSION_DIR%\extension.js"
set "BACKUP_FILE=%EXTENSION_DIR%\extension.js.backup"

:: 颜色代码
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "MAGENTA=[95m"
set "CYAN=[96m"
set "WHITE=[97m"
set "RESET=[0m"

:main
cls
echo %CYAN%================================================================%RESET%
echo %MAGENTA%🛠️  Augment扩展系统平台信息修改工具%RESET%
echo %CYAN%================================================================%RESET%
echo.
call :check_environment
if errorlevel 1 (
    echo %RED%❌ 环境检查失败，程序退出%RESET%
    pause
    exit /b 1
)

:menu
cls
echo %CYAN%================================================================%RESET%
echo %MAGENTA%🛠️  Augment扩展系统平台信息修改工具%RESET%
echo %CYAN%================================================================%RESET%
echo %WHITE%1. 修改为 macOS (Apple Silicon)%RESET%
echo %WHITE%2. 修改为 Windows 11%RESET%
echo %WHITE%3. 修改为 Linux%RESET%
echo %WHITE%4. 恢复原始文件%RESET%
echo %WHITE%5. 查看当前状态%RESET%
echo %WHITE%0. 退出%RESET%
echo %CYAN%================================================================%RESET%
echo.

set /p "choice=请选择操作 (0-5): "

if "%choice%"=="0" goto exit
if "%choice%"=="1" goto modify_darwin
if "%choice%"=="2" goto modify_win32
if "%choice%"=="3" goto modify_linux
if "%choice%"=="4" goto restore_backup
if "%choice%"=="5" goto show_status

echo %RED%❌ 无效选择，请重试%RESET%
timeout /t 2 >nul
goto menu

:modify_darwin
echo.
echo %BLUE%🎯 准备修改为: macOS (Apple Silicon)%RESET%
call :create_backup
if errorlevel 1 goto menu_pause

call :apply_modification "darwin" "arm64"
if errorlevel 1 goto menu_pause

echo.
echo %GREEN%🎉 成功! 系统平台信息已修改为: macOS (Apple Silicon)%RESET%
echo.
echo %YELLOW%📝 修改内容:%RESET%
echo   • Analytics平台: darwin
echo   • Analytics架构: arm64
echo.
call :show_notes
goto menu_pause

:modify_win32
echo.
echo %BLUE%🎯 准备修改为: Windows 11%RESET%
call :create_backup
if errorlevel 1 goto menu_pause

call :apply_modification "win32" "x64"
if errorlevel 1 goto menu_pause

echo.
echo %GREEN%🎉 成功! 系统平台信息已修改为: Windows 11%RESET%
echo.
echo %YELLOW%📝 修改内容:%RESET%
echo   • Analytics平台: win32
echo   • Analytics架构: x64
echo.
call :show_notes
goto menu_pause

:modify_linux
echo.
echo %BLUE%🎯 准备修改为: Linux%RESET%
call :create_backup
if errorlevel 1 goto menu_pause

call :apply_modification "linux" "x64"
if errorlevel 1 goto menu_pause

echo.
echo %GREEN%🎉 成功! 系统平台信息已修改为: Linux%RESET%
echo.
echo %YELLOW%📝 修改内容:%RESET%
echo   • Analytics平台: linux
echo   • Analytics架构: x64
echo.
call :show_notes
goto menu_pause

:restore_backup
echo.
echo %BLUE%🔄 恢复备份...%RESET%
if not exist "%BACKUP_FILE%" (
    echo %RED%❌ 错误: 备份文件不存在: %BACKUP_FILE%%RESET%
    goto menu_pause
)

copy /y "%BACKUP_FILE%" "%EXTENSION_JS%" >nul
if errorlevel 1 (
    echo %RED%❌ 恢复失败%RESET%
    goto menu_pause
)

echo %GREEN%✅ 恢复成功: %EXTENSION_JS%%RESET%
echo.
echo %GREEN%🎉 成功! 已恢复为原始文件%RESET%
echo.
echo %YELLOW%📝 恢复内容:%RESET%
echo   • 所有修改已撤销
echo   • 系统平台信息恢复为真实值
echo.
echo %YELLOW%⚠️  注意: 请重启VSCode以使恢复生效%RESET%
goto menu_pause

:show_status
echo.
echo %BLUE%📊 当前状态:%RESET%
echo   扩展目录: %EXTENSION_DIR%
if exist "%EXTENSION_JS%" (
    echo   目标文件: %GREEN%✅ 存在%RESET%
) else (
    echo   目标文件: %RED%❌ 不存在%RESET%
)
if exist "%BACKUP_FILE%" (
    echo   备份文件: %GREEN%✅ 存在%RESET%
) else (
    echo   备份文件: %RED%❌ 不存在%RESET%
)

if exist "%EXTENSION_JS%" (
    for %%F in ("%EXTENSION_JS%") do (
        echo   文件大小: %%~zF 字节
        echo   修改时间: %%~tF
    )
)
goto menu_pause

:check_environment
echo %BLUE%🔍 检查运行环境...%RESET%

if not exist "%EXTENSION_DIR%" (
    echo %RED%❌ 错误: 扩展目录不存在: %EXTENSION_DIR%%RESET%
    exit /b 1
)

if not exist "%EXTENSION_JS%" (
    echo %RED%❌ 错误: extension.js文件不存在: %EXTENSION_JS%%RESET%
    exit /b 1
)

echo %GREEN%✅ 扩展目录: %EXTENSION_DIR%%RESET%
echo %GREEN%✅ 目标文件: %EXTENSION_JS%%RESET%
exit /b 0

:create_backup
echo.
echo %BLUE%📦 创建备份...%RESET%

if exist "%BACKUP_FILE%" (
    echo %YELLOW%⚠️  备份文件已存在: %BACKUP_FILE%%RESET%
    set /p "overwrite=是否覆盖现有备份? (y/N): "
    if /i not "!overwrite!"=="y" (
        echo %RED%❌ 取消操作%RESET%
        exit /b 1
    )
)

copy /y "%EXTENSION_JS%" "%BACKUP_FILE%" >nul
if errorlevel 1 (
    echo %RED%❌ 备份创建失败%RESET%
    exit /b 1
)

echo %GREEN%✅ 备份创建成功: %BACKUP_FILE%%RESET%
exit /b 0

:apply_modification
set "platform=%~1"
set "arch=%~2"

echo.
echo %BLUE%🔧 应用修改...%RESET%

:: 创建临时PowerShell脚本进行精确替换
set "PS_SCRIPT=%TEMP%\modify_reveal.ps1"

(
echo $filePath = '%EXTENSION_JS%'
echo $content = Get-Content $filePath -Raw
echo.
echo # 修改Analytics平台信息
echo $pattern = 'platform:[a-zA-Z_$][a-zA-Z0-9_$]*\.platform\(\),arch:[a-zA-Z_$][a-zA-Z0-9_$]*\.arch\(\)'
echo $replacement = 'platform:"%platform%",arch:"%arch%"'
echo $content = $content -replace $pattern, $replacement
echo.
echo # 修改User-Agent平台信息
echo $pattern = '\`\$\{[^}]+\.platform\(\)\};\s*\$\{[^}]+\.arch\(\)\};\s*\$\{[^}]+\.release\(\)\}\`'
echo $replacement = '\`%platform%; %arch%; %release%\`'
echo $content = $content -replace $pattern, $replacement
echo.
echo Set-Content $filePath $content -Encoding UTF8
echo Write-Host "修改完成"
) > "%PS_SCRIPT%"

powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" 2>nul
if errorlevel 1 (
    echo %RED%❌ 修改应用失败%RESET%
    del "%PS_SCRIPT%" 2>nul
    exit /b 1
)

del "%PS_SCRIPT%" 2>nul
echo %GREEN%✅ 修改应用成功%RESET%
exit /b 0

:show_notes
echo %YELLOW%⚠️  注意事项:%RESET%
echo   • 请重启VSCode以使修改生效
echo   • 扩展更新时需要重新应用修改
echo   • 终端功能使用真实系统信息，不受影响
exit /b 0

:menu_pause
echo.
pause
goto menu

:exit
echo.
echo %GREEN%👋 再见!%RESET%
timeout /t 2 >nul
exit /b 0
