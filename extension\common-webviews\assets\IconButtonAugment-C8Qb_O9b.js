import{af as Be,c as Je,H as A,_ as fe,r as ke,n as Ge,p as Ke,aH as Fe,aI as Pe,aJ as Le,aK as de,q as Qe,ag as ue,aL as Ye,m as W,s as Ee,aM as he,aN as me,e as Ie,aO as Xe,aP as Ze,aQ as et,ac as tt,aR as st,aS as nt,Y as ze,aT as ot,aU as at,ae as it,aV as J,ar as rt,P as X,ak as lt,G as C,a9 as ct,a3 as K,aW as Q,aX as dt,aY as ut,l as M,A as Me,B as v,C as Te,I as He,J as Ve,K as ve,a as gt,L as pt,aZ as ft,M as ht,O as mt,t as Y,ab as P,b as z,S as Oe,N as Z,aA as vt,Q as Rt,R as ee,V as Re,D as te,F as We,a4 as Ue,a5 as wt}from"./SpinnerAugment-kH3m-zOb.js";let qt=!1;function Ut(e,t){return t}function _t(e,t,s,n,g,l=null){var o=e,q={flags:t,items:new Map,first:null};!(t&Fe)||(o=e.appendChild(Be()));var p=null,R=!1,a=fe(()=>{var w=s();return Le(w)?w:w==null?[]:Pe(w)});Je(()=>{var w=A(a),f=w.length;R&&f===0||(R=f===0,function(i,c,x,T,G,r,k){var Se,be,Ae,ye;var b,E,L,F,u,h,U=!!(G&Ye),_e=!!(G&(he|me)),se=i.length,_=c.items,Ne=c.first,m=Ne,S=null,y=[],I=[];if(U)for(h=0;h<se;h+=1)F=r(L=i[h],h),(u=_.get(F))!==void 0&&((Se=u.a)==null||Se.measure(),(E??(E=new Set)).add(u));for(h=0;h<se;h+=1)if(F=r(L=i[h],h),(u=_.get(F))!==void 0){if(_e&&St(u,L,h,G),u.e.f&de&&(ke(u.e),U&&((be=u.a)==null||be.unfix(),(E??(E=new Set)).delete(u))),u!==m){if(b!==void 0&&b.has(u)){if(y.length<I.length){var $,N=I[0];S=N.prev;var qe=y[0],ne=y[y.length-1];for($=0;$<y.length;$+=1)$e(y[$],N,x);for($=0;$<I.length;$+=1)b.delete(I[$]);D(c,qe.prev,ne.next),D(c,S,qe),D(c,ne,N),m=N,S=ne,h-=1,y=[],I=[]}else b.delete(u),$e(u,m,x),D(c,u.prev,u.next),D(c,u,S===null?c.first:S.next),D(c,S,u),S=u;continue}for(y=[],I=[];m!==null&&m.k!==F;)m.e.f&de||(b??(b=new Set)).add(m),I.push(m),m=m.next;if(m===null)continue;u=m}y.push(u),S=u,m=u.next}else S=bt(m?m.e.nodes_start:x,c,S,S===null?c.first:S.next,L,F,h,T,G,k),_.set(F,S),y=[],I=[],m=S.next;if(m!==null||b!==void 0){for(var H=b===void 0?[]:Pe(b);m!==null;)m.e.f&de||H.push(m),m=m.next;var oe=H.length;if(oe>0){var je=G&Fe&&se===0?x:null;if(U){for(h=0;h<oe;h+=1)(Ae=H[h].a)==null||Ae.measure();for(h=0;h<oe;h+=1)(ye=H[h].a)==null||ye.fix()}(function(V,O,ae,Ce){for(var ie=[],j=O.length,re=0;re<j;re++)Xe(O[re].e,ie,!0);var le=j>0&&ie.length===0&&ae!==null;if(le){var xe=ae.parentNode;Ze(xe),xe.append(ae),Ce.clear(),D(V,O[0].prev,O[j-1].next)}et(ie,()=>{for(var ce=0;ce<j;ce++){var B=O[ce];le||(Ce.delete(B.k),D(V,B.prev,B.next)),tt(B.e,!le)}})})(c,H,je,_)}}U&&Qe(()=>{var V;if(E!==void 0)for(u of E)(V=u.a)==null||V.apply()}),ue.first=c.first&&c.first.e,ue.last=S&&S.e}(w,q,o,g,t,n,s),l!==null&&(f===0?p?ke(p):p=Ge(()=>l(o)):p!==null&&Ke(p,()=>{p=null})),A(a))})}function St(e,t,s,n){n&he&&Ie(e.v,t),n&me?Ie(e.i,s):e.i=s}function bt(e,t,s,n,g,l,o,q,p,R){var a=p&he?p&st?Ee(g):W(g,!1,!1):g,w=p&me?Ee(o):o,f={i:w,v:a,k:l,a:null,e:null,prev:s,next:n};try{return f.e=Ge(()=>q(e,a,w,R),qt),f.e.prev=s&&s.e,f.e.next=n&&n.e,s===null?t.first=f:(s.next=f,s.e.next=f.e),n!==null&&(n.prev=f,n.e.prev=f.e),f}finally{}}function $e(e,t,s){for(var n=e.next?e.next.e.nodes_start:s,g=t?t.e.nodes_start:s,l=e.e.nodes_start;l!==n;){var o=nt(l);g.before(l),l=o}}function D(e,t,s){t===null?e.first=s:(t.next=s,t.e.next=s&&s.e),s!==null&&(s.prev=t,s.e.prev=t&&t.e)}function Nt(e,t,s=!1,n=!1,g=!1){var l=e,o="";ze(()=>{var q=ue;if(o!==(o=t()??"")&&(q.nodes_start!==null&&(ot(q.nodes_start,q.nodes_end),q.nodes_start=q.nodes_end=null),o!=="")){var p=o+"";s?p=`<svg>${p}</svg>`:n&&(p=`<math>${p}</math>`);var R=at(p);if((s||n)&&(R=J(R)),it(J(R),R.lastChild),s||n)for(;J(R);)l.before(J(R));else l.before(R)}})}function jt(e,t,s){rt(()=>{var n=X(()=>t(e,s==null?void 0:s())||{});if(s&&(n!=null&&n.update)){var g=!1,l={};lt(()=>{var o=s();C(o),g&&ct(l,o)&&(l=o,n.update(o))}),g=!0}if(n!=null&&n.destroy)return()=>n.destroy()})}function d(e,t){var l;var s=(l=e.$$events)==null?void 0:l[t.type],n=Le(s)?s.slice():s==null?[]:[s];for(var g of n)g.call(this,t)}var At=(e=>(e.asyncWrapper="async-wrapper",e.historyLoaded="history-loaded",e.historyInitialize="history-initialize",e.completionRating="completion-rating",e.completionRatingDone="completion-rating-done",e.nextEditRating="next-edit-rating",e.nextEditRatingDone="next-edit-rating-done",e.completions="completions",e.historyConfig="history-config",e.copyRequestID="copy-request-id-to-clipboard",e.openFile="open-file",e.openDiffInBuffer="open-diff-in-buffer",e.saveFile="save-file",e.loadFile="load-file",e.importFileRequest="import-file-request",e.importDirectoryRequest="import-directory-request",e.triggerImportDialogRequest="trigger-import-dialog-request",e.triggerImportDialogResponse="trigger-import-dialog-response",e.openMemoriesFile="open-memories-file",e.openMemoryDatabaseStateFile="open-memory-database-state-file",e.openAndEditFile="open-and-edit-file",e.diffViewNotifyReinit="diff-view-notify-reinit",e.diffViewLoaded="diff-view-loaded",e.diffViewInitialize="diff-view-initialize",e.diffViewResolveChunk="diff-view-resolve-chunk",e.diffViewFetchPendingStream="diff-view-fetch-pending-stream",e.diffViewDiffStreamStarted="diff-view-diff-stream-started",e.diffViewDiffStreamChunk="diff-view-diff-stream-chunk",e.diffViewDiffStreamEnded="diff-view-diff-stream-ended",e.diffViewAcceptAllChunks="diff-view-accept-all-chunks",e.diffViewAcceptFocusedChunk="diff-view-accept-selected-chunk",e.diffViewRejectFocusedChunk="diff-view-reject-focused-chunk",e.diffViewFocusPrevChunk="diff-view-focus-prev-chunk",e.diffViewFocusNextChunk="diff-view-focus-next-chunk",e.diffViewWindowFocusChange="diff-view-window-focus-change",e.diffViewFileFocus="diff-view-file-focus",e.disposeDiffView="dispose-diff-view",e.reportWebviewClientMetric="report-webview-client-metric",e.trackAnalyticsEvent="track-analytics-event",e.reportError="report-error",e.showNotification="show-notification",e.openConfirmationModal="open-confirmation-modal",e.confirmationModalResponse="confirmation-modal-response",e.clientTools="client-tools",e.currentlyOpenFiles="currently-open-files",e.findFileRequest="find-file-request",e.resolveFileRequest="resolve-file-request",e.findFileResponse="find-file-response",e.resolveFileResponse="resolve-file-response",e.findRecentlyOpenedFilesRequest="find-recently-opened-files",e.findRecentlyOpenedFilesResponse="find-recently-opened-files-response",e.findFolderRequest="find-folder-request",e.findFolderResponse="find-folder-response",e.findExternalSourcesRequest="find-external-sources-request",e.findExternalSourcesResponse="find-external-sources-response",e.findSymbolRequest="find-symbol-request",e.findSymbolRegexRequest="find-symbol-regex-request",e.findSymbolResponse="find-symbol-response",e.fileRangesSelected="file-ranges-selected",e.getDiagnosticsRequest="get-diagnostics-request",e.getDiagnosticsResponse="get-diagnostics-response",e.resolveWorkspaceFileChunkRequest="resolve-workspace-file-chunk",e.resolveWorkspaceFileChunkResponse="resolve-workspace-file-chunk-response",e.sourceFoldersUpdated="source-folders-updated",e.sourceFoldersSyncStatus="source-folders-sync-status",e.syncEnabledState="sync-enabled-state",e.shouldShowSummary="should-show-summary",e.showAugmentPanel="show-augment-panel",e.updateGuidelinesState="update-guidelines-state",e.openGuidelines="open-guidelines",e.updateWorkspaceGuidelines="update-workspace-guidelines",e.updateUserGuidelines="update-user-guidelines",e.chatAgentEditListHasUpdates="chat-agent-edit-list-has-updates",e.chatMemoryHasUpdates="chat-memory-has-updates",e.getAgentEditContentsByRequestId="getAgentEditContentsByRequestId",e.chatModeChanged="chat-mode-changed",e.chatClearMetadata="chat-clear-metadata",e.chatLoaded="chat-loaded",e.chatInitialize="chat-initialize",e.chatGetStreamRequest="chat-get-stream-request",e.chatUserMessage="chat-user-message",e.generateCommitMessage="generate-commit-message",e.chatUserCancel="chat-user-cancel",e.chatModelReply="chat-model-reply",e.chatInstructionMessage="chat-instruction-message",e.chatInstructionModelReply="chat-instruction-model-reply",e.chatCreateFile="chat-create-file",e.chatSmartPaste="chat-smart-paste",e.chatRating="chat-rating",e.chatRatingDone="chat-rating-done",e.chatStreamDone="chat-stream-done",e.runSlashCommand="run-slash-command",e.callTool="call-tool",e.callToolResponse="call-tool-response",e.cancelToolRun="cancel-tool-run",e.cancelToolRunResponse="cancel-tool-run-response",e.toolCheckSafe="check-safe",e.toolCheckSafeResponse="check-safe-response",e.checkToolExists="checkToolExists",e.checkToolExistsResponse="checkToolExistsResponse",e.startRemoteMCPAuth="start-remote-mcp-auth",e.getToolCallCheckpoint="get-tool-call-checkpoint",e.getToolCallCheckpointResponse="get-tool-call-checkpoint-response",e.updateAditionalChatModels="update-additional-chat-models",e.saveChat="save-chat",e.saveChatDone="save-chat-done",e.newThread="new-thread",e.chatSaveImageRequest="chat-save-image-request",e.chatSaveImageResponse="chat-save-image-response",e.chatLoadImageRequest="chat-load-image-request",e.chatLoadImageResponse="chat-load-image-response",e.chatDeleteImageRequest="chat-delete-image-request",e.chatDeleteImageResponse="chat-delete-image-response",e.chatSaveAttachmentRequest="chat-save-attachment-request",e.chatSaveAttachmentResponse="chat-save-attachment-response",e.instructions="instructions",e.nextEditDismiss="next-edit-dismiss",e.nextEditLoaded="next-edit-loaded",e.nextEditSuggestions="next-edit-suggestions",e.nextEditSuggestionsAction="next-edit-suggestions-action",e.nextEditRefreshStarted="next-edit-refresh-started",e.nextEditRefreshFinished="next-edit-refresh-finished",e.nextEditCancel="next-edit-cancel",e.nextEditPreviewActive="next-edit-preview-active",e.nextEditSuggestionsChanged="next-edit-suggestions-changed",e.nextEditNextSuggestionChanged="next-edit-next-suggestion-changed",e.nextEditOpenSuggestion="next-edit-open-suggestion",e.nextEditToggleSuggestionTree="next-edit-toggle-suggestion-tree",e.nextEditActiveSuggestionChanged="next-edit-active-suggestion",e.nextEditPanelFocus="next-edit-panel-focus",e.onboardingLoaded="onboarding-loaded",e.onboardingUpdateState="onboarding-update-state",e.usedChat="used-chat",e.preferencePanelLoaded="preference-panel-loaded",e.preferenceInit="preference-init",e.preferenceResultMessage="preference-result-message",e.preferenceNotify="preference-notify",e.openSettingsPage="open-settings-page",e.settingsPanelLoaded="settings-panel-loaded",e.navigateToSettingsSection="navigate-to-settings-section",e.mainPanelDisplayApp="main-panel-display-app",e.mainPanelLoaded="main-panel-loaded",e.mainPanelActions="main-panel-actions",e.mainPanelPerformAction="main-panel-perform-action",e.mainPanelCreateProject="main-panel-create-project",e.usedSlashAction="used-slash-action",e.signInLoaded="sign-in-loaded",e.signInLoadedResponse="sign-in-loaded-response",e.signOut="sign-out",e.awaitingSyncingPermissionLoaded="awaiting-syncing-permission-loaded",e.awaitingSyncingPermissionInitialize="awaiting-syncing-permission-initialize",e.readFileRequest="read-file-request",e.readFileResponse="read-file-response",e.wsContextGetChildrenRequest="ws-context-get-children-request",e.wsContextGetChildrenResponse="ws-context-get-children-response",e.wsContextGetSourceFoldersRequest="ws-context-get-source-folders-request",e.wsContextGetSourceFoldersResponse="ws-context-get-source-folders-response",e.wsContextAddMoreSourceFolders="ws-context-add-more-source-folders",e.wsContextRemoveSourceFolder="ws-context-remove-source-folder",e.wsContextSourceFoldersChanged="ws-context-source-folders-changed",e.wsContextFolderContentsChanged="ws-context-folder-contents-changed",e.wsContextUserRequestedRefresh="ws-context-user-requested-refresh",e.augmentLink="augment-link",e.resetAgentOnboarding="reset-agent-onboarding",e.empty="empty",e.chatGetAgentOnboardingPromptRequest="chat-get-agent-onboarding-prompt-request",e.chatGetAgentOnboardingPromptResponse="chat-get-agent-onboarding-prompt-response",e.getWorkspaceInfoRequest="get-workspace-info-request",e.getWorkspaceInfoResponse="get-workspace-info-response",e.getRemoteAgentOverviewsRequest="get-remote-agent-overviews-request",e.getRemoteAgentOverviewsResponse="get-remote-agent-overviews-response",e.remoteAgentOverviewsStreamRequest="remote-agent-overviews-stream-request",e.remoteAgentOverviewsStreamResponse="remote-agent-overviews-stream-response",e.getRemoteAgentChatHistoryRequest="get-remote-agent-chat-history-request",e.getRemoteAgentChatHistoryResponse="get-remote-agent-chat-history-response",e.remoteAgentHistoryStreamRequest="remote-agent-history-stream-request",e.remoteAgentHistoryStreamResponse="remote-agent-history-stream-response",e.cancelRemoteAgentsStreamRequest="cancel-remote-agents-stream-request",e.createRemoteAgentRequest="create-remote-agent-request",e.createRemoteAgentResponse="create-remote-agent-response",e.deleteRemoteAgentRequest="delete-remote-agent-request",e.deleteRemoteAgentResponse="delete-remote-agent-response",e.remoteAgentChatRequest="remote-agent-chat-request",e.remoteAgentChatResponse="remote-agent-chat-response",e.remoteAgentInterruptRequest="remote-agent-interrupt-request",e.remoteAgentInterruptResponse="remote-agent-interrupt-response",e.listSetupScriptsRequest="list-setup-scripts-request",e.listSetupScriptsResponse="list-setup-scripts-response",e.saveSetupScriptRequest="save-setup-script-request",e.saveSetupScriptResponse="save-setup-script-response",e.deleteSetupScriptRequest="delete-setup-script-request",e.deleteSetupScriptResponse="delete-setup-script-response",e.renameSetupScriptRequest="rename-setup-script-request",e.renameSetupScriptResponse="rename-setup-script-response",e.remoteAgentSshRequest="remote-agent-ssh-request",e.remoteAgentSshResponse="remote-agent-ssh-response",e.setRemoteAgentNotificationEnabled="set-remote-agent-notification-enabled",e.getRemoteAgentNotificationEnabledRequest="get-remote-agent-notification-enabled-request",e.getRemoteAgentNotificationEnabledResponse="get-remote-agent-notification-enabled-response",e.deleteRemoteAgentNotificationEnabled="delete-remote-agent-notification-enabled",e.setRemoteAgentPinnedStatus="set-remote-agent-pinned-status",e.getRemoteAgentPinnedStatusRequest="get-remote-agent-pinned-status-request",e.getRemoteAgentPinnedStatusResponse="get-remote-agent-pinned-status-response",e.deleteRemoteAgentPinnedStatus="delete-remote-agent-pinned-status",e.remoteAgentNotifyReady="remote-agent-notify-ready",e.remoteAgentSelectAgentId="remote-agent-select-agent-id",e.remoteAgentWorkspaceLogsRequest="remote-agent-workspace-logs-request",e.remoteAgentWorkspaceLogsResponse="remote-agent-workspace-logs-response",e.remoteAgentPauseRequest="remote-agent-pause-request",e.remoteAgentResumeRequest="remote-agent-resume-request",e.remoteAgentResumeHintRequest="remote-agent-resume-hint-request",e.updateRemoteAgentRequest="update-remote-agent-request",e.updateRemoteAgentResponse="update-remote-agent-response",e.updateSharedWebviewState="update-shared-webview-state",e.getSharedWebviewState="get-shared-webview-state",e.getSharedWebviewStateResponse="get-shared-webview-state-response",e.getGitBranchesRequest="get-git-branches-request",e.getGitBranchesResponse="get-git-branches-response",e.gitFetchRequest="git-fetch-request",e.gitFetchResponse="git-fetch-response",e.isGitRepositoryRequest="is-git-repository-request",e.isGitRepositoryResponse="is-git-repository-response",e.getWorkspaceDiffRequest="get-workspace-diff-request",e.getWorkspaceDiffResponse="get-workspace-diff-response",e.getRemoteUrlRequest="get-remote-url-request",e.getRemoteUrlResponse="get-remote-url-response",e.diffExplanationRequest="get-diff-explanation-request",e.diffExplanationResponse="get-diff-explanation-response",e.diffGroupChangesRequest="get-diff-group-changes-request",e.diffGroupChangesResponse="get-diff-group-changes-response",e.diffDescriptionsRequest="get-diff-descriptions-request",e.diffDescriptionsResponse="get-diff-descriptions-response",e.canApplyChangesRequest="can-apply-changes-request",e.canApplyChangesResponse="can-apply-changes-response",e.applyChangesRequest="apply-changes-request",e.applyChangesResponse="apply-changes-response",e.previewApplyChangesRequest="preview-apply-changes-request",e.previewApplyChangesResponse="preview-apply-changes-response",e.openFileRequest="open-file-request",e.openFileResponse="open-file-response",e.stashUnstagedChangesRequest="stash-unstaged-changes-request",e.stashUnstagedChangesResponse="stash-unstaged-changes-response",e.isGithubAuthenticatedRequest="is-github-authenticated-request",e.isGithubAuthenticatedResponse="is-github-authenticated-response",e.authenticateGithubRequest="authenticate-github-request",e.authenticateGithubResponse="authenticate-github-response",e.revokeGithubAccessRequest="revoke-github-access-request",e.revokeGithubAccessResponse="revoke-github-access-response",e.listGithubReposForAuthenticatedUserRequest="list-github-repos-for-authenticated-user-request",e.listGithubReposForAuthenticatedUserResponse="list-github-repos-for-authenticated-user-response",e.listGithubRepoBranchesRequest="list-github-repo-branches-request",e.listGithubRepoBranchesResponse="list-github-repo-branches-response",e.getGithubRepoRequest="get-github-repo-request",e.getGithubRepoResponse="get-github-repo-response",e.getCurrentLocalBranchRequest="get-current-local-branch-request",e.getCurrentLocalBranchResponse="get-current-local-branch-response",e.remoteAgentDiffPanelLoaded="remote-agent-diff-panel-loaded",e.remoteAgentDiffPanelSetOpts="remote-agent-diff-panel-set-opts",e.showRemoteAgentDiffPanel="show-remote-agent-diff-panel",e.closeRemoteAgentDiffPanel="close-remote-agent-diff-panel",e.remoteAgentHomePanelLoaded="remote-agent-home-panel-loaded",e.showRemoteAgentHomePanel="show-remote-agent-home-panel",e.closeRemoteAgentHomePanel="close-remote-agent-home-panel",e.triggerInitialOrientation="trigger-initial-orientation",e.executeInitialOrientation="execute-initial-orientation",e.orientationStatusUpdate="orientation-status-update",e.getOrientationStatus="get-orientation-status",e.checkAgentAutoModeApproval="check-agent-auto-mode-approval",e.checkAgentAutoModeApprovalResponse="check-agent-auto-mode-approval-response",e.setAgentAutoModeApproved="set-agent-auto-mode-approved",e.toolConfigLoaded="tool-config-loaded",e.toolConfigInitialize="tool-config-initialize",e.toolConfigSave="tool-config-save",e.toolConfigGetDefinitions="tool-config-get-definitions",e.toolConfigDefinitionsResponse="tool-config-definitions-response",e.toolConfigStartOAuth="tool-config-start-oauth",e.toolConfigStartOAuthResponse="tool-config-start-oauth-response",e.toolConfigRevokeAccess="tool-config-revoke-access",e.toolApprovalConfigSetRequest="tool-approval-config-set-request",e.toolApprovalConfigGetRequest="tool-approval-config-get-request",e.toolApprovalConfigGetResponse="tool-approval-config-get-response",e.getStoredMCPServers="get-stored-mcp-servers",e.setStoredMCPServers="set-stored-mcp-servers",e.getStoredMCPServersResponse="get-stored-mcp-servers-response",e.getChatRequestIdeStateRequest="get-ide-state-node-request",e.getChatRequestIdeStateResponse="get-ide-state-node-response",e.executeCommand="execute-command",e.toggleCollapseUnchangedRegions="toggle-collapse-unchanged-regions",e.openScratchFileRequest="open-scratch-file-request",e.getTerminalSettings="get-terminal-settings",e.terminalSettingsResponse="terminal-settings-response",e.updateTerminalSettings="update-terminal-settings",e.canShowTerminal="can-show-terminal",e.canShowTerminalResponse="can-show-terminal-response",e.showTerminal="show-terminal",e.showTerminalResponse="show-terminal-response",e.getRemoteAgentStatus="get-remote-agent-status",e.remoteAgentStatusResponse="remote-agent-status-response",e.remoteAgentStatusChanged="remote-agent-status-changed",e.saveLastRemoteAgentSetupRequest="save-last-remote-agent-setup-request",e.getLastRemoteAgentSetupRequest="get-last-remote-agent-setup-request",e.getLastRemoteAgentSetupResponse="get-last-remote-agent-setup-response",e.rulesLoaded="rules-loaded",e.memoriesLoaded="memories-loaded",e.getRulesListResponse="get-rules-list-response",e.getSubscriptionInfo="get-subscription-info",e.getSubscriptionInfoResponse="get-subscription-info-response",e.reportRemoteAgentEvent="report-remote-agent-event",e.reportAgentChangesApplied="report-agent-changes-applied",e.setPermissionToWriteToSSHConfig="set-permission-to-write-to-ssh-config",e.getShouldShowSSHConfigPermissionPromptRequest="get-should-show-ssh-config-permission-prompt-request",e.getShouldShowSSHConfigPermissionPromptResponse="get-should-show-ssh-config-permission-prompt-response",e))(At||{}),yt=(e=>(e.off="off",e.visibleHover="visible-hover",e.visible="visible",e.on="on",e))(yt||{}),Ct=(e=>(e.accept="accept",e.reject="reject",e))(Ct||{}),xt=(e=>(e.loading="loading",e.signIn="sign-in",e.chat="chat",e.workspaceContext="workspace-context",e.awaitingSyncingPermission="awaiting-syncing-permission",e.folderSelection="folder-selection",e))(xt||{}),kt=(e=>(e.idle="idle",e.inProgress="in-progress",e.succeeded="succeeded",e.failed="failed",e.aborted="aborted",e))(kt||{}),Ft=(e=>(e.included="included",e.excluded="excluded",e.partial="partial",e))(Ft||{}),Pt=(e=>(e[e.unspecified=0]="unspecified",e[e.typingMessage=1]="typingMessage",e[e.viewingAgent=2]="viewingAgent",e))(Pt||{}),we=(e=>(e.vscode="vscode",e.jetbrains="jetbrains",e.web="web",e))(we||{});const ge="data-vscode-theme-kind";function Et(){return self.acquireVsCodeApi!==void 0}function De(){dt(function(){const e=document.body.getAttribute(ge);if(e)return $t[e]}()),ut(function(){const e=document.body.getAttribute(ge);if(e)return Dt[e]}())}function It(){if(self.acquireVsCodeApi===void 0)throw new Error("acquireVsCodeAPI not available");return function(){new MutationObserver(De).observe(document.body,{attributeFilter:[ge],attributes:!0}),De()}(),{...self.acquireVsCodeApi(),clientType:we.vscode}}const $t={"vscode-dark":K.dark,"vscode-high-contrast":K.dark,"vscode-light":K.light,"vscode-high-contrast-light":K.light},Dt={"vscode-dark":Q.regular,"vscode-light":Q.regular,"vscode-high-contrast":Q.highContrast,"vscode-high-contrast-light":Q.highContrast};function Gt(){return window.augment_intellij!==void 0}function Lt(){var e;if(Et())return It();if(Gt())return function(){const t=window.augment_intellij;if(t===void 0||t.setState===void 0||t.getState===void 0||t.postMessage===void 0)throw new Error("Augment IntelliJ host not available");window.augment=window.augment||{};let s=!1;return window.augment.host={clientType:we.jetbrains,setState:n=>{s||console.error("Host not initialized"),t.setState(n)},getState:()=>(s||console.error("Host not initialized"),t.getState()),postMessage:n=>{s||console.error("Host not initialized"),t.postMessage(n)},initialize:async()=>{await t.initializationPromise,s=!0}},window.augment.host}();if(!((e=window.augment)!=null&&e.host))throw new Error("Augment host not available");return window.augment.host}function zt(){var e;return(e=window.augment)!=null&&e.host||(window.augment=window.augment||{},window.augment.host=Lt()),window.augment.host}const Bt=zt();function pe(e){return String(e).replace(".","_")}var Mt=ve('<div class="c-base-btn__loading svelte-5auyf2"><!></div> <span class="c-base-btn__hidden-content svelte-5auyf2"><!></span>',1),Tt=ve("<button><!></button>");function Ht(e,t){const s=M(t,["children","$$slots","$$events","$$legacy"]),n=M(s,["size","variant","color","disabled","highContrast","loading","alignment","radius"]);Me(t,!1);const g=W(),l=W();let o=v(t,"size",8,2),q=v(t,"variant",8,"solid"),p=v(t,"color",8,"accent"),R=v(t,"disabled",8,!1),a=v(t,"highContrast",8,!1),w=v(t,"loading",8,!1),f=v(t,"alignment",8,"center"),i=v(t,"radius",8,"medium");Te(()=>(A(g),A(l),C(n)),()=>{te(g,n.class),te(l,We(n,["class"]))}),He(),Ve();var c=Tt();gt(c,(r,k,b,E)=>({...r,...k,class:b,disabled:R()||w(),...A(l),[ht]:E}),[()=>pt(p()),()=>ft(i()),()=>(C(o()),C(q()),C(p()),A(g),C(f()),X(()=>`c-base-btn c-base-btn--size-${pe(o())} c-base-btn--${q()} c-base-btn--${p()} ${A(g)} c-base-btn--alignment-${f()}`)),()=>({"c-base-btn--highContrast":a(),"c-base-btn--loading":w()})],"svelte-5auyf2");var x=Y(c),T=r=>{var k=Mt(),b=Z(k),E=Y(b);const L=fe(()=>(C(o()),X(()=>function(h){switch(h){case 0:case .5:case 1:return 1;case 2:case 3:return 2;case 4:return 3}}(o()))));vt(E,{get size(){return A(L)}});var F=Rt(b,2),u=Y(F);ee(u,t,"default",{},null),z(r,k)},G=r=>{var k=Re(),b=Z(k);ee(b,t,"default",{},null),z(r,k)};mt(x,r=>{w()?r(T):r(G,!1)}),P("click",c,function(r){d.call(this,t,r)}),P("keyup",c,function(r){d.call(this,t,r)}),P("keydown",c,function(r){d.call(this,t,r)}),P("mousedown",c,function(r){d.call(this,t,r)}),P("mouseover",c,function(r){d.call(this,t,r)}),P("focus",c,function(r){d.call(this,t,r)}),P("mouseleave",c,function(r){d.call(this,t,r)}),P("blur",c,function(r){d.call(this,t,r)}),P("contextmenu",c,function(r){d.call(this,t,r)}),z(e,c),Oe()}var Vt=ve("<div><!></div>");function Ot(e,t){const s=M(t,["children","$$slots","$$events","$$legacy"]),n=M(s,["size","variant","color","highContrast","disabled","radius"]);Me(t,!1);const g=W(),l=W();let o=v(t,"size",8,2),q=v(t,"variant",8,"solid"),p=v(t,"color",8,"accent"),R=v(t,"highContrast",8,!1),a=v(t,"disabled",8,!1),w=v(t,"radius",8,"medium");Te(()=>(A(g),A(l),C(n)),()=>{te(g,n.class),te(l,We(n,["class"]))}),He(),Ve();var f=Vt();Ht(Y(f),Ue({get size(){return o()},get variant(){return q()},get color(){return p()},get highContrast(){return R()},get disabled(){return a()},get radius(){return w()},get class(){return A(g)}},()=>A(l),{$$events:{click(i){d.call(this,t,i)},keyup(i){d.call(this,t,i)},keydown(i){d.call(this,t,i)},mousedown(i){d.call(this,t,i)},mouseover(i){d.call(this,t,i)},focus(i){d.call(this,t,i)},mouseleave(i){d.call(this,t,i)},blur(i){d.call(this,t,i)},contextmenu(i){d.call(this,t,i)}},children:(i,c)=>{var x=Re(),T=Z(x);ee(T,t,"default",{},null),z(i,x)},$$slots:{default:!0}})),ze(i=>wt(f,1,i,"svelte-1mz435m"),[()=>(C(pe),C(o()),X(()=>`c-icon-btn c-icon-btn--size-${pe(o())}`))],fe),z(e,f),Oe()}function Jt(e,t){const s=M(t,["children","$$slots","$$events","$$legacy"]),n=M(s,["size","variant","color","highContrast","disabled","radius"]);let g=v(t,"size",8,2),l=v(t,"variant",8,"solid"),o=v(t,"color",8,"neutral"),q=v(t,"highContrast",8,!1),p=v(t,"disabled",8,!1),R=v(t,"radius",8,"medium");Ot(e,Ue({get size(){return g()},get variant(){return l()},get color(){return o()},get highContrast(){return q()},get disabled(){return p()},get radius(){return R()}},()=>n,{$$events:{click(a){d.call(this,t,a)},keyup(a){d.call(this,t,a)},keydown(a){d.call(this,t,a)},mousedown(a){d.call(this,t,a)},mouseover(a){d.call(this,t,a)},focus(a){d.call(this,t,a)},mouseleave(a){d.call(this,t,a)},blur(a){d.call(this,t,a)},contextmenu(a){d.call(this,t,a)}},children:(a,w)=>{var f=Re(),i=Z(f);ee(i,t,"default",{},null),z(a,f)},$$slots:{default:!0}}))}export{Ht as B,Ot as C,Ct as D,we as H,Jt as I,xt as M,kt as O,Pt as R,yt as S,At as W,jt as a,d as b,Bt as c,Ft as d,_t as e,Et as f,zt as g,Nt as h,Ut as i,Gt as j};
