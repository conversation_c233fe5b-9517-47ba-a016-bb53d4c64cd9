#!/bin/bash
# Augment扩展系统平台信息修改脚本 (Shell版本)
# 用于干扰扩展的系统平台信息检查，避免多账号检测

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 路径设置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
EXTENSION_DIR="$SCRIPT_DIR/extension/out"
EXTENSION_JS="$EXTENSION_DIR/extension.js"
BACKUP_FILE="$EXTENSION_DIR/extension.js.backup"

# 平台配置
declare -A PLATFORMS
PLATFORMS[darwin]="darwin|arm64|macOS (Apple Silicon)"
PLATFORMS[win32]="win32|x64|Windows 11"
PLATFORMS[linux]="linux|x64|Linux"

# 检查环境
check_environment() {
    echo -e "${BLUE}🔍 检查运行环境...${NC}"
    
    if [[ ! -d "$EXTENSION_DIR" ]]; then
        echo -e "${RED}❌ 错误: 扩展目录不存在: $EXTENSION_DIR${NC}"
        return 1
    fi
    
    if [[ ! -f "$EXTENSION_JS" ]]; then
        echo -e "${RED}❌ 错误: extension.js文件不存在: $EXTENSION_JS${NC}"
        return 1
    fi

    echo -e "${GREEN}✅ 扩展目录: $EXTENSION_DIR${NC}"
    echo -e "${GREEN}✅ 目标文件: $EXTENSION_JS${NC}"
    return 0
}

# 创建备份
create_backup() {
    echo -e "\n${BLUE}📦 创建备份...${NC}"
    
    if [[ -f "$BACKUP_FILE" ]]; then
        echo -e "${YELLOW}⚠️  备份文件已存在: $BACKUP_FILE${NC}"
        read -p "是否覆盖现有备份? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo -e "${RED}❌ 取消操作${NC}"
            return 1
        fi
    fi
    
    if cp "$EXTENSION_JS" "$BACKUP_FILE"; then
        echo -e "${GREEN}✅ 备份创建成功: $BACKUP_FILE${NC}"
        return 0
    else
        echo -e "${RED}❌ 备份创建失败${NC}"
        return 1
    fi
}

# 恢复备份
restore_backup() {
    echo -e "\n${BLUE}🔄 恢复备份...${NC}"
    
    if [[ ! -f "$BACKUP_FILE" ]]; then
        echo -e "${RED}❌ 错误: 备份文件不存在: $BACKUP_FILE${NC}"
        return 1
    fi
    
    if cp "$BACKUP_FILE" "$EXTENSION_JS"; then
        echo -e "${GREEN}✅ 恢复成功: $EXTENSION_JS${NC}"
        return 0
    else
        echo -e "${RED}❌ 恢复失败${NC}"
        return 1
    fi
}

# 应用修改
apply_modification() {
    local platform="$1"
    local arch="$2"
    
    echo -e "\n${BLUE}🔧 应用修改...${NC}"
    
    # 使用sed进行替换 - 针对压缩混淆的extension.js
    # 修改Analytics平台信息
    if sed -i.tmp -E "s/platform:[a-zA-Z_$][a-zA-Z0-9_$]*\.platform\(\),arch:[a-zA-Z_$][a-zA-Z0-9_$]*\.arch\(\)/platform:\"$platform\",arch:\"$arch\"/g" "$EXTENSION_JS"; then
        rm -f "$EXTENSION_JS.tmp" 2>/dev/null
        echo -e "${GREEN}✅ 修改应用成功${NC}"
        return 0
    else
        echo -e "${RED}❌ 修改应用失败${NC}"
        return 1
    fi
}

# 验证修改
verify_modification() {
    local platform="$1"
    local arch="$2"
    
    echo -e "\n${BLUE}🔍 验证修改结果...${NC}"
    
    if grep -q "platform:\"$platform\",arch:\"$arch\"" "$EXTENSION_JS"; then
        echo -e "${GREEN}✅ 修改验证成功${NC}"
        return 0
    else
        echo -e "${RED}❌ 修改验证失败${NC}"
        return 1
    fi
}

# 显示状态
show_status() {
    echo -e "\n${BLUE}📊 当前状态:${NC}"
    echo "  扩展目录: $EXTENSION_DIR"
    
    if [[ -f "$EXTENSION_JS" ]]; then
        echo -e "  目标文件: ${GREEN}✅ 存在${NC}"
        echo "  文件大小: $(wc -c < "$EXTENSION_JS") 字节"
        echo "  修改时间: $(stat -c %y "$EXTENSION_JS" 2>/dev/null || stat -f %Sm "$EXTENSION_JS" 2>/dev/null)"
    else
        echo -e "  目标文件: ${RED}❌ 不存在${NC}"
    fi
    
    if [[ -f "$BACKUP_FILE" ]]; then
        echo -e "  备份文件: ${GREEN}✅ 存在${NC}"
    else
        echo -e "  备份文件: ${RED}❌ 不存在${NC}"
    fi
}

# 显示注意事项
show_notes() {
    echo -e "${YELLOW}⚠️  注意事项:${NC}"
    echo "  • 请重启VSCode以使修改生效"
    echo "  • 扩展更新时需要重新应用修改"
    echo "  • 终端功能使用真实系统信息，不受影响"
}

# 修改平台信息
modify_platform() {
    local platform_key="$1"
    local platform_info="${PLATFORMS[$platform_key]}"
    
    IFS='|' read -r platform arch description <<< "$platform_info"
    
    echo -e "\n${BLUE}🎯 准备修改为: $description${NC}"
    
    # 创建备份
    if ! create_backup; then
        return 1
    fi
    
    # 应用修改
    if apply_modification "$platform" "$arch"; then
        # 验证修改
        if verify_modification "$platform" "$arch"; then
            echo -e "\n${GREEN}🎉 成功! 系统平台信息已修改为: $description${NC}"
            echo -e "\n${YELLOW}📝 修改内容:${NC}"
            echo "  • Analytics平台: $platform"
            echo "  • Analytics架构: $arch"
            echo
            show_notes
        else
            echo -e "\n${RED}❌ 修改验证失败，请检查文件内容${NC}"
            return 1
        fi
    else
        echo -e "\n${RED}❌ 修改应用失败${NC}"
        return 1
    fi
}

# 恢复原始文件
restore_original() {
    if restore_backup; then
        echo -e "\n${GREEN}🎉 成功! 已恢复为原始文件${NC}"
        echo -e "\n${YELLOW}📝 恢复内容:${NC}"
        echo "  • 所有修改已撤销"
        echo "  • 系统平台信息恢复为真实值"
        echo -e "\n${YELLOW}⚠️  注意: 请重启VSCode以使恢复生效${NC}"
    else
        echo -e "\n${RED}❌ 恢复失败${NC}"
    fi
}

# 显示菜单
show_menu() {
    clear
    echo -e "${CYAN}================================================================${NC}"
    echo -e "${MAGENTA}🛠️  Augment扩展系统平台信息修改工具${NC}"
    echo -e "${CYAN}================================================================${NC}"
    echo -e "${WHITE}1. 修改为 macOS (Apple Silicon)${NC}"
    echo -e "${WHITE}2. 修改为 Windows 11${NC}"
    echo -e "${WHITE}3. 修改为 Linux${NC}"
    echo -e "${WHITE}4. 恢复原始文件${NC}"
    echo -e "${WHITE}5. 查看当前状态${NC}"
    echo -e "${WHITE}0. 退出${NC}"
    echo -e "${CYAN}================================================================${NC}"
}

# 主程序
main() {
    echo -e "${GREEN}🚀 启动Augment扩展平台信息修改工具${NC}"
    
    if ! check_environment; then
        echo -e "\n${RED}❌ 环境检查失败，程序退出${NC}"
        exit 1
    fi
    
    while true; do
        show_menu
        echo
        read -p "请选择操作 (0-5): " choice
        
        case $choice in
            0)
                echo -e "\n${GREEN}👋 再见!${NC}"
                break
                ;;
            1)
                modify_platform "darwin"
                ;;
            2)
                modify_platform "win32"
                ;;
            3)
                modify_platform "linux"
                ;;
            4)
                restore_original
                ;;
            5)
                show_status
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请重试${NC}"
                sleep 2
                continue
                ;;
        esac
        
        echo
        read -p "按回车键继续..." -r
    done
}

# 运行主程序
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
