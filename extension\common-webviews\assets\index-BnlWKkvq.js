var no=Object.defineProperty;var so=(e,t,n)=>t in e?no(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var c=(e,t,n)=>so(e,typeof t!="symbol"?t+"":t,n);import{a as ao}from"./async-messaging-CCLqHBoR.js";import{W as _,S as ro,a as Pn,b as we,B as oo,h as io}from"./IconButtonAugment-C8Qb_O9b.js";import{R as ye,P as B,C as D,b as pn,I as Nt,a as G,E as lo}from"./message-broker-CwcPcQ_e.js";import{C as uo}from"./types-CGlLNakm.js";import{n as co,f as mo,i as ho}from"./file-paths-Bl_IgYJd.js";import{w as Xn,y as po,f as $a,b as A,aG as Ln,A as me,B as I,v as qa,u as Q,C as Te,I as Qe,J as he,ab as be,am as go,K as J,R as X,t as re,Y as et,a5 as tt,$ as _e,S as pe,W as Re,H as M,m as Ee,G as De,a0 as fo,D as Se,z as Ha,l as V,a4 as He,N as ve,O as dt,P as Ge,Q as mt,T as Ga,V as xe,a_ as _s,_ as Bt,ba as vs,F as Va,a as Ba,b0 as Ts,an as Ya,aC as yo,aa as bo,a7 as _o}from"./SpinnerAugment-kH3m-zOb.js";import{c as vo,e as Mt,f as To,C as Eo,a as Dt,R as So,b as je,g as Es}from"./CardAugment-BAmr_-U4.js";import{B as Io,b as No}from"./BaseTextInput-TeF8u93x.js";function Jn(e,t){return!(e===null||typeof e!="object"||!("$typeName"in e)||typeof e.$typeName!="string")&&(t===void 0||t.typeName===e.$typeName)}var p;function wo(){let e=0,t=0;for(let s=0;s<28;s+=7){let a=this.buf[this.pos++];if(e|=(127&a)<<s,!(128&a))return this.assertBounds(),[e,t]}let n=this.buf[this.pos++];if(e|=(15&n)<<28,t=(112&n)>>4,!(128&n))return this.assertBounds(),[e,t];for(let s=3;s<=31;s+=7){let a=this.buf[this.pos++];if(t|=(127&a)<<s,!(128&a))return this.assertBounds(),[e,t]}throw new Error("invalid varint")}function gn(e,t,n){for(let r=0;r<28;r+=7){const o=e>>>r,i=!(!(o>>>7)&&t==0),l=255&(i?128|o:o);if(n.push(l),!i)return}const s=e>>>28&15|(7&t)<<4,a=!!(t>>3);if(n.push(255&(a?128|s:s)),a){for(let r=3;r<31;r+=7){const o=t>>>r,i=!!(o>>>7),l=255&(i?128|o:o);if(n.push(l),!i)return}n.push(t>>>31&1)}}(function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"})(p||(p={}));const Ft=4294967296;function Ss(e){const t=e[0]==="-";t&&(e=e.slice(1));const n=1e6;let s=0,a=0;function r(o,i){const l=Number(e.slice(o,i));a*=n,s=s*n+l,s>=Ft&&(a+=s/Ft|0,s%=Ft)}return r(-24,-18),r(-18,-12),r(-12,-6),r(-6),t?ja(s,a):Zn(s,a)}function Is(e,t){if({lo:e,hi:t}=function(l,u){return{lo:l>>>0,hi:u>>>0}}(e,t),t<=2097151)return String(Ft*t+e);const n=16777215&(e>>>24|t<<8),s=t>>16&65535;let a=(16777215&e)+6777216*n+6710656*s,r=n+8147497*s,o=2*s;const i=1e7;return a>=i&&(r+=Math.floor(a/i),a%=i),r>=i&&(o+=Math.floor(r/i),r%=i),o.toString()+Ns(r)+Ns(a)}function Zn(e,t){return{lo:0|e,hi:0|t}}function ja(e,t){return t=~t,e?e=1+~e:t+=1,Zn(e,t)}const Ns=e=>{const t=String(e);return"0000000".slice(t.length)+t};function ws(e,t){if(e>=0){for(;e>127;)t.push(127&e|128),e>>>=7;t.push(e)}else{for(let n=0;n<9;n++)t.push(127&e|128),e>>=7;t.push(1)}}function Co(){let e=this.buf[this.pos++],t=127&e;if(!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<7,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<14,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<21,!(128&e))return this.assertBounds(),t;e=this.buf[this.pos++],t|=(15&e)<<28;for(let n=5;128&e&&n<10;n++)e=this.buf[this.pos++];if(128&e)throw new Error("invalid varint");return this.assertBounds(),t>>>0}var Cs={};const O=ko();function ko(){const e=new DataView(new ArrayBuffer(8));if(typeof BigInt=="function"&&typeof e.getBigInt64=="function"&&typeof e.getBigUint64=="function"&&typeof e.setBigInt64=="function"&&typeof e.setBigUint64=="function"&&(typeof process!="object"||typeof Cs!="object"||Cs.BUF_BIGINT_DISABLE!=="1")){const t=BigInt("-9223372036854775808"),n=BigInt("9223372036854775807"),s=BigInt("0"),a=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(r){const o=typeof r=="bigint"?r:BigInt(r);if(o>n||o<t)throw new Error(`invalid int64: ${r}`);return o},uParse(r){const o=typeof r=="bigint"?r:BigInt(r);if(o>a||o<s)throw new Error(`invalid uint64: ${r}`);return o},enc(r){return e.setBigInt64(0,this.parse(r),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},uEnc(r){return e.setBigInt64(0,this.uParse(r),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},dec:(r,o)=>(e.setInt32(0,r,!0),e.setInt32(4,o,!0),e.getBigInt64(0,!0)),uDec:(r,o)=>(e.setInt32(0,r,!0),e.setInt32(4,o,!0),e.getBigUint64(0,!0))}}return{zero:"0",supported:!1,parse:t=>(typeof t!="string"&&(t=t.toString()),ks(t),t),uParse:t=>(typeof t!="string"&&(t=t.toString()),Rs(t),t),enc:t=>(typeof t!="string"&&(t=t.toString()),ks(t),Ss(t)),uEnc:t=>(typeof t!="string"&&(t=t.toString()),Rs(t),Ss(t)),dec:(t,n)=>function(s,a){let r=Zn(s,a);const o=2147483648&r.hi;o&&(r=ja(r.lo,r.hi));const i=Is(r.lo,r.hi);return o?"-"+i:i}(t,n),uDec:(t,n)=>Is(t,n)}}function ks(e){if(!/^-?[0-9]+$/.test(e))throw new Error("invalid int64: "+e)}function Rs(e){if(!/^[0-9]+$/.test(e))throw new Error("invalid uint64: "+e)}function Ve(e,t){switch(e){case p.STRING:return"";case p.BOOL:return!1;case p.DOUBLE:case p.FLOAT:return 0;case p.INT64:case p.UINT64:case p.SFIXED64:case p.FIXED64:case p.SINT64:return t?"0":O.zero;case p.BYTES:return new Uint8Array(0);default:return 0}}const ke=Symbol.for("reflect unsafe local");function Ka(e,t){const n=e[t.localName].case;return n===void 0?n:t.fields.find(s=>s.localName===n)}function Ro(e,t){const n=t.localName;if(t.oneof)return e[t.oneof.localName].case===n;if(t.presence!=2)return e[n]!==void 0&&Object.prototype.hasOwnProperty.call(e,n);switch(t.fieldKind){case"list":return e[n].length>0;case"map":return Object.keys(e[n]).length>0;case"scalar":return!function(s,a){switch(s){case p.BOOL:return a===!1;case p.STRING:return a==="";case p.BYTES:return a instanceof Uint8Array&&!a.byteLength;default:return a==0}}(t.scalar,e[n]);case"enum":return e[n]!==t.enum.values[0].number}throw new Error("message field with implicit presence")}function ht(e,t){return Object.prototype.hasOwnProperty.call(e,t)&&e[t]!==void 0}function Wa(e,t){if(t.oneof){const n=e[t.oneof.localName];return n.case===t.localName?n.value:void 0}return e[t.localName]}function za(e,t,n){t.oneof?e[t.oneof.localName]={case:t.localName,value:n}:e[t.localName]=n}function Pe(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)}function Qn(e,t){var n,s,a,r;if(Pe(e)&&ke in e&&"add"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const o=t,i=e.field();return o.listKind==i.listKind&&o.scalar===i.scalar&&((n=o.message)===null||n===void 0?void 0:n.typeName)===((s=i.message)===null||s===void 0?void 0:s.typeName)&&((a=o.enum)===null||a===void 0?void 0:a.typeName)===((r=i.enum)===null||r===void 0?void 0:r.typeName)}return!0}return!1}function es(e,t){var n,s,a,r;if(Pe(e)&&ke in e&&"has"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const o=t,i=e.field();return o.mapKey===i.mapKey&&o.mapKind==i.mapKind&&o.scalar===i.scalar&&((n=o.message)===null||n===void 0?void 0:n.typeName)===((s=i.message)===null||s===void 0?void 0:s.typeName)&&((a=o.enum)===null||a===void 0?void 0:a.typeName)===((r=i.enum)===null||r===void 0?void 0:r.typeName)}return!0}return!1}function ts(e,t){return Pe(e)&&ke in e&&"desc"in e&&Pe(e.desc)&&e.desc.kind==="message"&&(t===void 0||e.desc.typeName==t.typeName)}function Tt(e){const t=e.fields[0];return Xa(e.typeName)&&t!==void 0&&t.fieldKind=="scalar"&&t.name=="value"&&t.number==1}function Xa(e){return e.startsWith("google.protobuf.")&&["DoubleValue","FloatValue","Int64Value","UInt64Value","Int32Value","UInt32Value","BoolValue","StringValue","BytesValue"].includes(e.substring(16))}const xo=999,Ao=998,wt=2;function fe(e,t){if(Jn(t,e))return t;const n=function(s){let a;if(function(r){switch(r.file.edition){case xo:return!1;case Ao:return!0;default:return r.fields.some(o=>o.presence!=wt&&o.fieldKind!="message"&&!o.oneof)}}(s)){const r=As.get(s);let o,i;if(r)({prototype:o,members:i}=r);else{o={},i=new Set;for(const l of s.members)l.kind!="oneof"&&(l.fieldKind!="scalar"&&l.fieldKind!="enum"||l.presence!=wt&&(i.add(l),o[l.localName]=fn(l)));As.set(s,{prototype:o,members:i})}a=Object.create(o),a.$typeName=s.typeName;for(const l of s.members)if(!i.has(l)){if(l.kind=="field"&&(l.fieldKind=="message"||(l.fieldKind=="scalar"||l.fieldKind=="enum")&&l.presence!=wt))continue;a[l.localName]=fn(l)}}else{a={$typeName:s.typeName};for(const r of s.members)r.kind!="oneof"&&r.presence!=wt||(a[r.localName]=fn(r))}return a}(e);return t!==void 0&&function(s,a,r){for(const o of s.members){let i,l=r[o.localName];if(l!=null){if(o.kind=="oneof"){const u=Ka(r,o);if(!u)continue;i=u,l=Wa(r,u)}else i=o;switch(i.fieldKind){case"message":l=ns(i,l);break;case"scalar":l=Ja(i,l);break;case"list":l=Mo(i,l);break;case"map":l=Oo(i,l)}za(a,i,l)}}}(e,n,t),n}function Ja(e,t){return e.scalar==p.BYTES?ss(t):t}function Oo(e,t){if(Pe(t)){if(e.scalar==p.BYTES)return xs(t,ss);if(e.mapKind=="message")return xs(t,n=>ns(e,n))}return t}function Mo(e,t){if(Array.isArray(t)){if(e.scalar==p.BYTES)return t.map(ss);if(e.listKind=="message")return t.map(n=>ns(e,n))}return t}function ns(e,t){if(e.fieldKind=="message"&&!e.oneof&&Tt(e.message))return Ja(e.message.fields[0],t);if(Pe(t)){if(e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!=="google.protobuf.Value")return t;if(!Jn(t,e.message))return fe(e.message,t)}return t}function ss(e){return Array.isArray(e)?new Uint8Array(e):e}function xs(e,t){const n={};for(const s of Object.entries(e))n[s[0]]=t(s[1]);return n}const Do=Symbol(),As=new WeakMap;function fn(e){if(e.kind=="oneof")return{case:void 0};if(e.fieldKind=="list")return[];if(e.fieldKind=="map")return{};if(e.fieldKind=="message")return Do;const t=e.getDefaultValue();return t!==void 0?e.fieldKind=="scalar"&&e.longAsString?t.toString():t:e.fieldKind=="scalar"?Ve(e.scalar,e.longAsString):e.enum.values[0].number}const Fo=["FieldValueInvalidError","FieldListRangeError","ForeignFieldError"];class ee extends Error{constructor(t,n,s="FieldValueInvalidError"){super(n),this.name=s,this.field=()=>t}}const yn=Symbol.for("@bufbuild/protobuf/text-encoding");function as(){if(globalThis[yn]==null){const e=new globalThis.TextEncoder,t=new globalThis.TextDecoder;globalThis[yn]={encodeUtf8:n=>e.encode(n),decodeUtf8:n=>t.decode(n),checkUtf8(n){try{return encodeURIComponent(n),!0}catch{return!1}}}}return globalThis[yn]}var F;(function(e){e[e.Varint=0]="Varint",e[e.Bit64=1]="Bit64",e[e.LengthDelimited=2]="LengthDelimited",e[e.StartGroup=3]="StartGroup",e[e.EndGroup=4]="EndGroup",e[e.Bit32=5]="Bit32"})(F||(F={}));const Za=34028234663852886e22,Qa=-34028234663852886e22,er=4294967295,tr=2147483647,nr=-2147483648;class sr{constructor(t=as().encodeUtf8){this.encodeUtf8=t,this.stack=[],this.chunks=[],this.buf=[]}finish(){this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]);let t=0;for(let a=0;a<this.chunks.length;a++)t+=this.chunks[a].length;let n=new Uint8Array(t),s=0;for(let a=0;a<this.chunks.length;a++)n.set(this.chunks[a],s),s+=this.chunks[a].length;return this.chunks=[],n}fork(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}join(){let t=this.finish(),n=this.stack.pop();if(!n)throw new Error("invalid state, fork stack empty");return this.chunks=n.chunks,this.buf=n.buf,this.uint32(t.byteLength),this.raw(t)}tag(t,n){return this.uint32((t<<3|n)>>>0)}raw(t){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(t),this}uint32(t){for(Os(t);t>127;)this.buf.push(127&t|128),t>>>=7;return this.buf.push(t),this}int32(t){return bn(t),ws(t,this.buf),this}bool(t){return this.buf.push(t?1:0),this}bytes(t){return this.uint32(t.byteLength),this.raw(t)}string(t){let n=this.encodeUtf8(t);return this.uint32(n.byteLength),this.raw(n)}float(t){(function(s){if(typeof s=="string"){const a=s;if(s=Number(s),Number.isNaN(s)&&a!=="NaN")throw new Error("invalid float32: "+a)}else if(typeof s!="number")throw new Error("invalid float32: "+typeof s);if(Number.isFinite(s)&&(s>Za||s<Qa))throw new Error("invalid float32: "+s)})(t);let n=new Uint8Array(4);return new DataView(n.buffer).setFloat32(0,t,!0),this.raw(n)}double(t){let n=new Uint8Array(8);return new DataView(n.buffer).setFloat64(0,t,!0),this.raw(n)}fixed32(t){Os(t);let n=new Uint8Array(4);return new DataView(n.buffer).setUint32(0,t,!0),this.raw(n)}sfixed32(t){bn(t);let n=new Uint8Array(4);return new DataView(n.buffer).setInt32(0,t,!0),this.raw(n)}sint32(t){return bn(t),ws(t=(t<<1^t>>31)>>>0,this.buf),this}sfixed64(t){let n=new Uint8Array(8),s=new DataView(n.buffer),a=O.enc(t);return s.setInt32(0,a.lo,!0),s.setInt32(4,a.hi,!0),this.raw(n)}fixed64(t){let n=new Uint8Array(8),s=new DataView(n.buffer),a=O.uEnc(t);return s.setInt32(0,a.lo,!0),s.setInt32(4,a.hi,!0),this.raw(n)}int64(t){let n=O.enc(t);return gn(n.lo,n.hi,this.buf),this}sint64(t){const n=O.enc(t),s=n.hi>>31;return gn(n.lo<<1^s,(n.hi<<1|n.lo>>>31)^s,this.buf),this}uint64(t){const n=O.uEnc(t);return gn(n.lo,n.hi,this.buf),this}}class rs{constructor(t,n=as().decodeUtf8){this.decodeUtf8=n,this.varint64=wo,this.uint32=Co,this.buf=t,this.len=t.length,this.pos=0,this.view=new DataView(t.buffer,t.byteOffset,t.byteLength)}tag(){let t=this.uint32(),n=t>>>3,s=7&t;if(n<=0||s<0||s>5)throw new Error("illegal tag: field no "+n+" wire type "+s);return[n,s]}skip(t,n){let s=this.pos;switch(t){case F.Varint:for(;128&this.buf[this.pos++];);break;case F.Bit64:this.pos+=4;case F.Bit32:this.pos+=4;break;case F.LengthDelimited:let a=this.uint32();this.pos+=a;break;case F.StartGroup:for(;;){const[r,o]=this.tag();if(o===F.EndGroup){if(n!==void 0&&r!==n)throw new Error("invalid end group tag");break}this.skip(o,r)}break;default:throw new Error("cant skip wire type "+t)}return this.assertBounds(),this.buf.subarray(s,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return 0|this.uint32()}sint32(){let t=this.uint32();return t>>>1^-(1&t)}int64(){return O.dec(...this.varint64())}uint64(){return O.uDec(...this.varint64())}sint64(){let[t,n]=this.varint64(),s=-(1&t);return t=(t>>>1|(1&n)<<31)^s,n=n>>>1^s,O.dec(t,n)}bool(){let[t,n]=this.varint64();return t!==0||n!==0}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return O.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return O.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let t=this.uint32(),n=this.pos;return this.pos+=t,this.assertBounds(),this.buf.subarray(n,n+t)}string(){return this.decodeUtf8(this.bytes())}}function bn(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid int32: "+typeof e);if(!Number.isInteger(e)||e>tr||e<nr)throw new Error("invalid int32: "+e)}function Os(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid uint32: "+typeof e);if(!Number.isInteger(e)||e>er||e<0)throw new Error("invalid uint32: "+e)}function Le(e,t){const n=e.fieldKind=="list"?Qn(t,e):e.fieldKind=="map"?es(t,e):os(e,t);if(n===!0)return;let s;switch(e.fieldKind){case"list":s=`expected ${or(e)}, got ${$(t)}`;break;case"map":s=`expected ${ir(e)}, got ${$(t)}`;break;default:s=Yt(e,t,n)}return new ee(e,s)}function Ms(e,t,n){const s=os(e,n);if(s!==!0)return new ee(e,`list item #${t+1}: ${Yt(e,n,s)}`)}function os(e,t){return e.scalar!==void 0?ar(t,e.scalar):e.enum!==void 0?e.enum.open?Number.isInteger(t):e.enum.values.some(n=>n.number===t):ts(t,e.message)}function ar(e,t){switch(t){case p.DOUBLE:return typeof e=="number";case p.FLOAT:return typeof e=="number"&&(!(!Number.isNaN(e)&&Number.isFinite(e))||!(e>Za||e<Qa)||`${e.toFixed()} out of range`);case p.INT32:case p.SFIXED32:case p.SINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>tr||e<nr)||`${e.toFixed()} out of range`);case p.FIXED32:case p.UINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>er||e<0)||`${e.toFixed()} out of range`);case p.BOOL:return typeof e=="boolean";case p.STRING:return typeof e=="string"&&(as().checkUtf8(e)||"invalid UTF8");case p.BYTES:return e instanceof Uint8Array;case p.INT64:case p.SFIXED64:case p.SINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return O.parse(e),!0}catch{return`${e} out of range`}return!1;case p.FIXED64:case p.UINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return O.uParse(e),!0}catch{return`${e} out of range`}return!1}}function Yt(e,t,n){return n=typeof n=="string"?`: ${n}`:`, got ${$(t)}`,e.scalar!==void 0?`expected ${function(s){switch(s){case p.STRING:return"string";case p.BOOL:return"boolean";case p.INT64:case p.SINT64:case p.SFIXED64:return"bigint (int64)";case p.UINT64:case p.FIXED64:return"bigint (uint64)";case p.BYTES:return"Uint8Array";case p.DOUBLE:return"number (float64)";case p.FLOAT:return"number (float32)";case p.FIXED32:case p.UINT32:return"number (uint32)";case p.INT32:case p.SFIXED32:case p.SINT32:return"number (int32)"}}(e.scalar)}`+n:e.enum!==void 0?`expected ${e.enum.toString()}`+n:`expected ${rr(e.message)}`+n}function $(e){switch(typeof e){case"object":return e===null?"null":e instanceof Uint8Array?`Uint8Array(${e.length})`:Array.isArray(e)?`Array(${e.length})`:Qn(e)?or(e.field()):es(e)?ir(e.field()):ts(e)?rr(e.desc):Jn(e)?`message ${e.$typeName}`:"object";case"string":return e.length>30?"string":`"${e.split('"').join('\\"')}"`;case"boolean":case"number":return String(e);case"bigint":return String(e)+"n";default:return typeof e}}function rr(e){return`ReflectMessage (${e.typeName})`}function or(e){switch(e.listKind){case"message":return`ReflectList (${e.message.toString()})`;case"enum":return`ReflectList (${e.enum.toString()})`;case"scalar":return`ReflectList (${p[e.scalar]})`}}function ir(e){switch(e.mapKind){case"message":return`ReflectMap (${p[e.mapKey]}, ${e.message.toString()})`;case"enum":return`ReflectMap (${p[e.mapKey]}, ${e.enum.toString()})`;case"scalar":return`ReflectMap (${p[e.mapKey]}, ${p[e.scalar]})`}}function le(e,t,n=!0){return new lr(e,t,n)}class lr{get sortedFields(){var t;return(t=this._sortedFields)!==null&&t!==void 0?t:this._sortedFields=this.desc.fields.concat().sort((n,s)=>n.number-s.number)}constructor(t,n,s=!0){this.lists=new Map,this.maps=new Map,this.check=s,this.desc=t,this.message=this[ke]=n??fe(t),this.fields=t.fields,this.oneofs=t.oneofs,this.members=t.members}findNumber(t){return this._fieldsByNumber||(this._fieldsByNumber=new Map(this.desc.fields.map(n=>[n.number,n]))),this._fieldsByNumber.get(t)}oneofCase(t){return ot(this.message,t),Ka(this.message,t)}isSet(t){return ot(this.message,t),Ro(this.message,t)}clear(t){ot(this.message,t),function(n,s){const a=s.localName;if(s.oneof){const r=s.oneof.localName;n[r].case===a&&(n[r]={case:void 0})}else if(s.presence!=2)delete n[a];else switch(s.fieldKind){case"map":n[a]={};break;case"list":n[a]=[];break;case"enum":n[a]=s.enum.values[0].number;break;case"scalar":n[a]=Ve(s.scalar,s.longAsString)}}(this.message,t)}get(t){ot(this.message,t);const n=Wa(this.message,t);switch(t.fieldKind){case"list":let s=this.lists.get(t);return s&&s[ke]===n||this.lists.set(t,s=new Uo(t,n,this.check)),s;case"map":let a=this.maps.get(t);return a&&a[ke]===n||this.maps.set(t,a=new Po(t,n,this.check)),a;case"message":return ls(t,n,this.check);case"scalar":return n===void 0?Ve(t.scalar,!1):us(t,n);case"enum":return n??t.enum.values[0].number}}set(t,n){if(ot(this.message,t),this.check){const a=Le(t,n);if(a)throw a}let s;s=t.fieldKind=="message"?is(t,n):es(n)||Qn(n)?n[ke]:cs(t,n),za(this.message,t,s)}getUnknown(){return this.message.$unknown}setUnknown(t){this.message.$unknown=t}}function ot(e,t){if(t.parent.typeName!==e.$typeName)throw new ee(t,`cannot use ${t.toString()} with message ${e.$typeName}`,"ForeignFieldError")}class Uo{field(){return this._field}get size(){return this._arr.length}constructor(t,n,s){this._field=t,this._arr=this[ke]=n,this.check=s}get(t){const n=this._arr[t];return n===void 0?void 0:_n(this._field,n,this.check)}set(t,n){if(t<0||t>=this._arr.length)throw new ee(this._field,`list item #${t+1}: out of range`);if(this.check){const s=Ms(this._field,t,n);if(s)throw s}this._arr[t]=Ds(this._field,n)}add(t){if(this.check){const n=Ms(this._field,this._arr.length,t);if(n)throw n}this._arr.push(Ds(this._field,t))}clear(){this._arr.splice(0,this._arr.length)}[Symbol.iterator](){return this.values()}keys(){return this._arr.keys()}*values(){for(const t of this._arr)yield _n(this._field,t,this.check)}*entries(){for(let t=0;t<this._arr.length;t++)yield[t,_n(this._field,this._arr[t],this.check)]}}class Po{constructor(t,n,s=!0){this.obj=this[ke]=n??{},this.check=s,this._field=t}field(){return this._field}set(t,n){if(this.check){const s=function(a,r,o){const i=ar(r,a.mapKey);if(i!==!0)return new ee(a,`invalid map key: ${Yt({scalar:a.mapKey},r,i)}`);const l=os(a,o);return l!==!0?new ee(a,`map entry ${$(r)}: ${Yt(a,o,l)}`):void 0}(this._field,t,n);if(s)throw s}return this.obj[Ct(t)]=function(s,a){return s.mapKind=="message"?is(s,a):cs(s,a)}(this._field,n),this}delete(t){const n=Ct(t),s=Object.prototype.hasOwnProperty.call(this.obj,n);return s&&delete this.obj[n],s}clear(){for(const t of Object.keys(this.obj))delete this.obj[t]}get(t){let n=this.obj[Ct(t)];return n!==void 0&&(n=vn(this._field,n,this.check)),n}has(t){return Object.prototype.hasOwnProperty.call(this.obj,Ct(t))}*keys(){for(const t of Object.keys(this.obj))yield Fs(t,this._field.mapKey)}*entries(){for(const t of Object.entries(this.obj))yield[Fs(t[0],this._field.mapKey),vn(this._field,t[1],this.check)]}[Symbol.iterator](){return this.entries()}get size(){return Object.keys(this.obj).length}*values(){for(const t of Object.values(this.obj))yield vn(this._field,t,this.check)}forEach(t,n){for(const s of this.entries())t.call(n,s[1],s[0],this)}}function is(e,t){return ts(t)?Xa(t.message.$typeName)&&!e.oneof&&e.fieldKind=="message"?t.message.value:t.desc.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"?cr(t.message):t.message:t}function ls(e,t,n){return t!==void 0&&(Tt(e.message)&&!e.oneof&&e.fieldKind=="message"?t={$typeName:e.message.typeName,value:us(e.message.fields[0],t)}:e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"&&Pe(t)&&(t=ur(t))),new lr(e.message,t,n)}function Ds(e,t){return e.listKind=="message"?is(e,t):cs(e,t)}function _n(e,t,n){return e.listKind=="message"?ls(e,t,n):us(e,t)}function vn(e,t,n){return e.mapKind=="message"?ls(e,t,n):t}function Ct(e){return typeof e=="string"||typeof e=="number"?e:String(e)}function Fs(e,t){switch(t){case p.STRING:return e;case p.INT32:case p.FIXED32:case p.UINT32:case p.SFIXED32:case p.SINT32:{const n=Number.parseInt(e);if(Number.isFinite(n))return n;break}case p.BOOL:switch(e){case"true":return!0;case"false":return!1}break;case p.UINT64:case p.FIXED64:try{return O.uParse(e)}catch{}break;default:try{return O.parse(e)}catch{}}return e}function us(e,t){switch(e.scalar){case p.INT64:case p.SFIXED64:case p.SINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=O.parse(t));break;case p.FIXED64:case p.UINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=O.uParse(t))}return t}function cs(e,t){switch(e.scalar){case p.INT64:case p.SFIXED64:case p.SINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=O.parse(t));break;case p.FIXED64:case p.UINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=O.uParse(t))}return t}function ur(e){const t={$typeName:"google.protobuf.Struct",fields:{}};if(Pe(e))for(const[n,s]of Object.entries(e))t.fields[n]=mr(s);return t}function cr(e){const t={};for(const[n,s]of Object.entries(e.fields))t[n]=dr(s);return t}function dr(e){switch(e.kind.case){case"structValue":return cr(e.kind.value);case"listValue":return e.kind.value.values.map(dr);case"nullValue":case void 0:return null;default:return e.kind.value}}function mr(e){const t={$typeName:"google.protobuf.Value",kind:{case:void 0}};switch(typeof e){case"number":t.kind={case:"numberValue",value:e};break;case"string":t.kind={case:"stringValue",value:e};break;case"boolean":t.kind={case:"boolValue",value:e};break;case"object":if(e===null)t.kind={case:"nullValue",value:0};else if(Array.isArray(e)){const n={$typeName:"google.protobuf.ListValue",values:[]};if(Array.isArray(e))for(const s of e)n.values.push(mr(s));t.kind={case:"listValue",value:n}}else t.kind={case:"structValue",value:ur(e)}}return t}function hr(e){const t=function(){if(!Ke){Ke=[];const l=pr("std");for(let u=0;u<l.length;u++)Ke[l[u].charCodeAt(0)]=u;Ke[45]=l.indexOf("+"),Ke[95]=l.indexOf("/")}return Ke}();let n=3*e.length/4;e[e.length-2]=="="?n-=2:e[e.length-1]=="="&&(n-=1);let s,a=new Uint8Array(n),r=0,o=0,i=0;for(let l=0;l<e.length;l++){if(s=t[e.charCodeAt(l)],s===void 0)switch(e[l]){case"=":o=0;case`
`:case"\r":case"	":case" ":continue;default:throw Error("invalid base64 string")}switch(o){case 0:i=s,o=1;break;case 1:a[r++]=i<<2|(48&s)>>4,i=s,o=2;break;case 2:a[r++]=(15&i)<<4|(60&s)>>2,i=s,o=3;break;case 3:a[r++]=(3&i)<<6|s,o=0}}if(o==1)throw Error("invalid base64 string");return a.subarray(0,r)}let kt,Us,Ke;function pr(e){return kt||(kt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),Us=kt.slice(0,-2).concat("-","_")),e=="url"?Us:kt}function yt(e){let t=!1;const n=[];for(let s=0;s<e.length;s++){let a=e.charAt(s);switch(a){case"_":t=!0;break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":n.push(a),t=!1;break;default:t&&(t=!1,a=a.toUpperCase()),n.push(a)}}return n.join("")}const Lo=new Set(["constructor","toString","toJSON","valueOf"]);function bt(e){return Lo.has(e)?e+"$":e}function ds(e){for(const t of e.field)ht(t,"jsonName")||(t.jsonName=yt(t.name));e.nestedType.forEach(ds)}function $o(e,t){switch(e){case p.STRING:return t;case p.BYTES:{const n=function(s){const a=[],r={tail:s,c:"",next(){return this.tail.length!=0&&(this.c=this.tail[0],this.tail=this.tail.substring(1),!0)},take(o){if(this.tail.length>=o){const i=this.tail.substring(0,o);return this.tail=this.tail.substring(o),i}return!1}};for(;r.next();)if(r.c==="\\"){if(r.next())switch(r.c){case"\\":a.push(r.c.charCodeAt(0));break;case"b":a.push(8);break;case"f":a.push(12);break;case"n":a.push(10);break;case"r":a.push(13);break;case"t":a.push(9);break;case"v":a.push(11);break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":{const o=r.c,i=r.take(2);if(i===!1)return!1;const l=parseInt(o+i,8);if(Number.isNaN(l))return!1;a.push(l);break}case"x":{const o=r.c,i=r.take(2);if(i===!1)return!1;const l=parseInt(o+i,16);if(Number.isNaN(l))return!1;a.push(l);break}case"u":{const o=r.c,i=r.take(4);if(i===!1)return!1;const l=parseInt(o+i,16);if(Number.isNaN(l))return!1;const u=new Uint8Array(4);new DataView(u.buffer).setInt32(0,l,!0),a.push(u[0],u[1],u[2],u[3]);break}case"U":{const o=r.c,i=r.take(8);if(i===!1)return!1;const l=O.uEnc(o+i),u=new Uint8Array(8),m=new DataView(u.buffer);m.setInt32(0,l.lo,!0),m.setInt32(4,l.hi,!0),a.push(u[0],u[1],u[2],u[3],u[4],u[5],u[6],u[7]);break}}}else a.push(r.c.charCodeAt(0));return new Uint8Array(a)}(t);if(n===!1)throw new Error(`cannot parse ${p[e]} default value: ${t}`);return n}case p.INT64:case p.SFIXED64:case p.SINT64:return O.parse(t);case p.UINT64:case p.FIXED64:return O.uParse(t);case p.DOUBLE:case p.FLOAT:switch(t){case"inf":return Number.POSITIVE_INFINITY;case"-inf":return Number.NEGATIVE_INFINITY;case"nan":return Number.NaN;default:return parseFloat(t)}case p.BOOL:return t==="true";case p.INT32:case p.UINT32:case p.SINT32:case p.FIXED32:case p.SFIXED32:return parseInt(t,10)}}function*$n(e){switch(e.kind){case"file":for(const t of e.messages)yield t,yield*$n(t);yield*e.enums,yield*e.services,yield*e.extensions;break;case"message":for(const t of e.nestedMessages)yield t,yield*$n(t);yield*e.nestedEnums,yield*e.nestedExtensions}}function gr(...e){const t=function(){const n=new Map,s=new Map,a=new Map;return{kind:"registry",types:n,extendees:s,[Symbol.iterator]:()=>n.values(),get files(){return a.values()},addFile(r,o,i){if(a.set(r.proto.name,r),!o)for(const l of $n(r))this.add(l);if(i)for(const l of r.dependencies)this.addFile(l,o,i)},add(r){if(r.kind=="extension"){let o=s.get(r.extendee.typeName);o||s.set(r.extendee.typeName,o=new Map),o.set(r.number,r)}n.set(r.typeName,r)},get:r=>n.get(r),getFile:r=>a.get(r),getMessage(r){const o=n.get(r);return(o==null?void 0:o.kind)=="message"?o:void 0},getEnum(r){const o=n.get(r);return(o==null?void 0:o.kind)=="enum"?o:void 0},getExtension(r){const o=n.get(r);return(o==null?void 0:o.kind)=="extension"?o:void 0},getExtensionFor(r,o){var i;return(i=s.get(r.typeName))===null||i===void 0?void 0:i.get(o)},getService(r){const o=n.get(r);return(o==null?void 0:o.kind)=="service"?o:void 0}}}();if(!e.length)return t;if("$typeName"in e[0]&&e[0].$typeName=="google.protobuf.FileDescriptorSet"){for(const n of e[0].file)$s(n,t);return t}if("$typeName"in e[0]){let r=function(o){const i=[];for(const l of o.dependency){if(t.getFile(l)!=null||a.has(l))continue;const u=s(l);if(!u)throw new Error(`Unable to resolve ${l}, imported by ${o.name}`);"kind"in u?t.addFile(u,!1,!0):(a.add(u.name),i.push(u))}return i.concat(...i.map(r))};const n=e[0],s=e[1],a=new Set;for(const o of[n,...r(n)].reverse())$s(o,t)}else for(const n of e)for(const s of n.files)t.addFile(s);return t}const qo=998,Ho=999,Go=9,Ut=10,ut=11,Vo=12,Ps=14,qn=3,Bo=2,Ls=1,Yo=0,jo=1,Ko=2,Wo=3,zo=1,Xo=2,Jo=1,fr={998:{fieldPresence:1,enumType:2,repeatedFieldEncoding:2,utf8Validation:3,messageEncoding:1,jsonFormat:2,enforceNamingStyle:2},999:{fieldPresence:2,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2},1e3:{fieldPresence:1,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2}};function $s(e,t){var n,s;const a={kind:"file",proto:e,deprecated:(s=(n=e.options)===null||n===void 0?void 0:n.deprecated)!==null&&s!==void 0&&s,edition:ei(e),name:e.name.replace(/\.proto$/,""),dependencies:ti(e,t),enums:[],messages:[],extensions:[],services:[],toString:()=>`file ${e.name}`},r=new Map,o={get:i=>r.get(i),add(i){var l;ge(((l=i.proto.options)===null||l===void 0?void 0:l.mapEntry)===!0),r.set(i.typeName,i)}};for(const i of e.enumType)yr(i,a,void 0,t);for(const i of e.messageType)br(i,a,void 0,t,o);for(const i of e.service)Zo(i,a,t);Hn(a,t);for(const i of r.values())Gn(i,t,o);for(const i of a.messages)Gn(i,t,o),Hn(i,t);t.addFile(a,!0)}function Hn(e,t){switch(e.kind){case"file":for(const n of e.proto.extension){const s=Vn(n,e,t);e.extensions.push(s),t.add(s)}break;case"message":for(const n of e.proto.extension){const s=Vn(n,e,t);e.nestedExtensions.push(s),t.add(s)}for(const n of e.nestedMessages)Hn(n,t)}}function Gn(e,t,n){const s=e.proto.oneofDecl.map(r=>function(o,i){return{kind:"oneof",proto:o,deprecated:!1,parent:i,fields:[],name:o.name,localName:bt(yt(o.name)),toString(){return`oneof ${i.typeName}.${this.name}`}}}(r,e)),a=new Set;for(const r of e.proto.field){const o=ni(r,s),i=Vn(r,e,t,o,n);e.fields.push(i),e.field[i.localName]=i,o===void 0?e.members.push(i):(o.fields.push(i),a.has(o)||(a.add(o),e.members.push(o)))}for(const r of s.filter(o=>a.has(o)))e.oneofs.push(r);for(const r of e.nestedMessages)Gn(r,t,n)}function yr(e,t,n,s){var a,r,o,i,l;const u=function(d,h){const g=(f=d,(f.substring(0,1)+f.substring(1).replace(/[A-Z]/g,b=>"_"+b)).toLowerCase()+"_");var f;for(const b of h){if(!b.name.toLowerCase().startsWith(g))return;const y=b.name.substring(g.length);if(y.length==0||/^\d/.test(y))return}return g}(e.name,e.value),m={kind:"enum",proto:e,deprecated:(r=(a=e.options)===null||a===void 0?void 0:a.deprecated)!==null&&r!==void 0&&r,file:t,parent:n,open:!0,name:e.name,typeName:un(e,n,t),value:{},values:[],sharedPrefix:u,toString(){return`enum ${this.typeName}`}};m.open=function(d){var h;return Jo==st("enumType",{proto:d.proto,parent:(h=d.parent)!==null&&h!==void 0?h:d.file})}(m),s.add(m);for(const d of e.value){const h=d.name;m.values.push(m.value[d.number]={kind:"enum_value",proto:d,deprecated:(i=(o=e.options)===null||o===void 0?void 0:o.deprecated)!==null&&i!==void 0&&i,parent:m,name:h,localName:bt(u==null?h:h.substring(u.length)),number:d.number,toString:()=>`enum value ${m.typeName}.${h}`})}((l=n==null?void 0:n.nestedEnums)!==null&&l!==void 0?l:t.enums).push(m)}function br(e,t,n,s,a){var r,o,i,l;const u={kind:"message",proto:e,deprecated:(o=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&o!==void 0&&o,file:t,parent:n,name:e.name,typeName:un(e,n,t),fields:[],field:{},oneofs:[],members:[],nestedEnums:[],nestedMessages:[],nestedExtensions:[],toString(){return`message ${this.typeName}`}};((i=e.options)===null||i===void 0?void 0:i.mapEntry)===!0?a.add(u):(((l=n==null?void 0:n.nestedMessages)!==null&&l!==void 0?l:t.messages).push(u),s.add(u));for(const m of e.enumType)yr(m,t,u,s);for(const m of e.nestedType)br(m,t,u,s,a)}function Zo(e,t,n){var s,a;const r={kind:"service",proto:e,deprecated:(a=(s=e.options)===null||s===void 0?void 0:s.deprecated)!==null&&a!==void 0&&a,file:t,name:e.name,typeName:un(e,void 0,t),methods:[],method:{},toString(){return`service ${this.typeName}`}};t.services.push(r),n.add(r);for(const o of e.method){const i=Qo(o,r,n);r.methods.push(i),r.method[i.localName]=i}}function Qo(e,t,n){var s,a,r,o;let i;i=e.clientStreaming&&e.serverStreaming?"bidi_streaming":e.clientStreaming?"client_streaming":e.serverStreaming?"server_streaming":"unary";const l=n.getMessage(Ce(e.inputType)),u=n.getMessage(Ce(e.outputType));ge(l,`invalid MethodDescriptorProto: input_type ${e.inputType} not found`),ge(u,`invalid MethodDescriptorProto: output_type ${e.inputType} not found`);const m=e.name;return{kind:"rpc",proto:e,deprecated:(a=(s=e.options)===null||s===void 0?void 0:s.deprecated)!==null&&a!==void 0&&a,parent:t,name:m,localName:bt(m.length?bt(m[0].toLowerCase()+m.substring(1)):m),methodKind:i,input:l,output:u,idempotency:(o=(r=e.options)===null||r===void 0?void 0:r.idempotencyLevel)!==null&&o!==void 0?o:Yo,toString:()=>`rpc ${t.typeName}.${m}`}}function Vn(e,t,n,s,a){var r,o,i;const l=a===void 0,u={kind:"field",proto:e,deprecated:(o=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&o!==void 0&&o,name:e.name,number:e.number,scalar:void 0,message:void 0,enum:void 0,presence:si(e,s,l,t),listKind:void 0,mapKind:void 0,mapKey:void 0,delimitedEncoding:void 0,packed:void 0,longAsString:!1,getDefaultValue:void 0};if(l){const g=t.kind=="file"?t:t.file,f=t.kind=="file"?void 0:t,b=un(e,f,g);u.kind="extension",u.file=g,u.parent=f,u.oneof=void 0,u.typeName=b,u.jsonName=`[${b}]`,u.toString=()=>`extension ${b}`;const y=n.getMessage(Ce(e.extendee));ge(y,`invalid FieldDescriptorProto: extendee ${e.extendee} not found`),u.extendee=y}else{const g=t;ge(g.kind=="message"),u.parent=g,u.oneof=s,u.localName=s?yt(e.name):bt(yt(e.name)),u.jsonName=e.jsonName,u.toString=()=>`field ${g.typeName}.${e.name}`}const m=e.label,d=e.type,h=(i=e.options)===null||i===void 0?void 0:i.jstype;if(m===qn){const g=d==ut?a==null?void 0:a.get(Ce(e.typeName)):void 0;if(g){u.fieldKind="map";const{key:f,value:b}=function(y){const v=y.fields.find(N=>N.number===1),C=y.fields.find(N=>N.number===2);return ge(v&&v.fieldKind=="scalar"&&v.scalar!=p.BYTES&&v.scalar!=p.FLOAT&&v.scalar!=p.DOUBLE&&C&&C.fieldKind!="list"&&C.fieldKind!="map"),{key:v,value:C}}(g);return u.mapKey=f.scalar,u.mapKind=b.fieldKind,u.message=b.message,u.delimitedEncoding=!1,u.enum=b.enum,u.scalar=b.scalar,u}switch(u.fieldKind="list",d){case ut:case Ut:u.listKind="message",u.message=n.getMessage(Ce(e.typeName)),ge(u.message),u.delimitedEncoding=qs(e,t);break;case Ps:u.listKind="enum",u.enum=n.getEnum(Ce(e.typeName)),ge(u.enum);break;default:u.listKind="scalar",u.scalar=d,u.longAsString=h==Ls}return u.packed=function(f,b){if(f.label!=qn)return!1;switch(f.type){case Go:case Vo:case Ut:case ut:return!1}const y=f.options;return y&&ht(y,"packed")?y.packed:zo==st("repeatedFieldEncoding",{proto:f,parent:b})}(e,t),u}switch(d){case ut:case Ut:u.fieldKind="message",u.message=n.getMessage(Ce(e.typeName)),ge(u.message,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),u.delimitedEncoding=qs(e,t),u.getDefaultValue=()=>{};break;case Ps:{const g=n.getEnum(Ce(e.typeName));ge(g!==void 0,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),u.fieldKind="enum",u.enum=n.getEnum(Ce(e.typeName)),u.getDefaultValue=()=>ht(e,"defaultValue")?function(f,b){const y=f.values.find(v=>v.name===b);if(!y)throw new Error(`cannot parse ${f} default value: ${b}`);return y.number}(g,e.defaultValue):void 0;break}default:u.fieldKind="scalar",u.scalar=d,u.longAsString=h==Ls,u.getDefaultValue=()=>ht(e,"defaultValue")?$o(d,e.defaultValue):void 0}return u}function ei(e){switch(e.syntax){case"":case"proto2":return qo;case"proto3":return Ho;case"editions":if(e.edition in fr)return e.edition;throw new Error(`${e.name}: unsupported edition`);default:throw new Error(`${e.name}: unsupported syntax "${e.syntax}"`)}}function ti(e,t){return e.dependency.map(n=>{const s=t.getFile(n);if(!s)throw new Error(`Cannot find ${n}, imported by ${e.name}`);return s})}function un(e,t,n){let s;return s=t?`${t.typeName}.${e.name}`:n.proto.package.length>0?`${n.proto.package}.${e.name}`:`${e.name}`,s}function Ce(e){return e.startsWith(".")?e.substring(1):e}function ni(e,t){if(!ht(e,"oneofIndex")||e.proto3Optional)return;const n=t[e.oneofIndex];return ge(n,`invalid FieldDescriptorProto: oneof #${e.oneofIndex} for field #${e.number} not found`),n}function si(e,t,n,s){return e.label==Bo?Wo:e.label==qn?Ko:t||e.proto3Optional||e.type==ut||n?jo:st("fieldPresence",{proto:e,parent:s})}function qs(e,t){return e.type==Ut||Xo==st("messageEncoding",{proto:e,parent:t})}function st(e,t){var n,s;const a=(n=t.proto.options)===null||n===void 0?void 0:n.features;if(a){const r=a[e];if(r!=0)return r}if("kind"in t){if(t.kind=="message")return st(e,(s=t.parent)!==null&&s!==void 0?s:t.file);const r=fr[t.edition];if(!r)throw new Error(`feature default for edition ${t.edition} not found`);return r[e]}return st(e,t.parent)}function ge(e,t){if(!e)throw new Error(t)}function ai(e){const t=function(n){return Object.assign(Object.create({syntax:"",edition:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FileDescriptorProto",dependency:[],publicDependency:[],weakDependency:[],service:[],extension:[]},n),{messageType:n.messageType.map(_r),enumType:n.enumType.map(vr)}))}(e);return t.messageType.forEach(ds),gr(t,()=>{}).getFile(t.name)}function _r(e){var t,n,s,a,r,o,i,l;return{$typeName:"google.protobuf.DescriptorProto",name:e.name,field:(n=(t=e.field)===null||t===void 0?void 0:t.map(ri))!==null&&n!==void 0?n:[],extension:[],nestedType:(a=(s=e.nestedType)===null||s===void 0?void 0:s.map(_r))!==null&&a!==void 0?a:[],enumType:(o=(r=e.enumType)===null||r===void 0?void 0:r.map(vr))!==null&&o!==void 0?o:[],extensionRange:(l=(i=e.extensionRange)===null||i===void 0?void 0:i.map(u=>Object.assign({$typeName:"google.protobuf.DescriptorProto.ExtensionRange"},u)))!==null&&l!==void 0?l:[],oneofDecl:[],reservedRange:[],reservedName:[]}}function ri(e){return Object.assign(Object.create({label:1,typeName:"",extendee:"",defaultValue:"",oneofIndex:0,jsonName:"",proto3Optional:!1}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldDescriptorProto"},e),{options:e.options?oi(e.options):void 0}))}function oi(e){var t,n,s;return Object.assign(Object.create({ctype:0,packed:!1,jstype:0,lazy:!1,unverifiedLazy:!1,deprecated:!1,weak:!1,debugRedact:!1,retention:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldOptions"},e),{targets:(t=e.targets)!==null&&t!==void 0?t:[],editionDefaults:(s=(n=e.editionDefaults)===null||n===void 0?void 0:n.map(r=>Object.assign({$typeName:"google.protobuf.FieldOptions.EditionDefault"},r)))!==null&&s!==void 0?s:[],uninterpretedOption:[]}))}function vr(e){return{$typeName:"google.protobuf.EnumDescriptorProto",name:e.name,reservedName:[],reservedRange:[],value:e.value.map(t=>Object.assign({$typeName:"google.protobuf.EnumValueDescriptorProto"},t))}}function Et(e,t,...n){return n.reduce((s,a)=>s.nestedMessages[a],e.messages[t])}const ii=Et(ai({name:"google/protobuf/descriptor.proto",package:"google.protobuf",messageType:[{name:"FileDescriptorSet",field:[{name:"file",number:1,type:11,label:3,typeName:".google.protobuf.FileDescriptorProto"}],extensionRange:[{start:536e6,end:536000001}]},{name:"FileDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"package",number:2,type:9,label:1},{name:"dependency",number:3,type:9,label:3},{name:"public_dependency",number:10,type:5,label:3},{name:"weak_dependency",number:11,type:5,label:3},{name:"message_type",number:4,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:5,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"service",number:6,type:11,label:3,typeName:".google.protobuf.ServiceDescriptorProto"},{name:"extension",number:7,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FileOptions"},{name:"source_code_info",number:9,type:11,label:1,typeName:".google.protobuf.SourceCodeInfo"},{name:"syntax",number:12,type:9,label:1},{name:"edition",number:14,type:14,label:1,typeName:".google.protobuf.Edition"}]},{name:"DescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"field",number:2,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"extension",number:6,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"nested_type",number:3,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"extension_range",number:5,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ExtensionRange"},{name:"oneof_decl",number:8,type:11,label:3,typeName:".google.protobuf.OneofDescriptorProto"},{name:"options",number:7,type:11,label:1,typeName:".google.protobuf.MessageOptions"},{name:"reserved_range",number:9,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ReservedRange"},{name:"reserved_name",number:10,type:9,label:3}],nestedType:[{name:"ExtensionRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ExtensionRangeOptions"}]},{name:"ReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"ExtensionRangeOptions",field:[{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"},{name:"declaration",number:2,type:11,label:3,typeName:".google.protobuf.ExtensionRangeOptions.Declaration",options:{retention:2}},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"verification",number:3,type:14,label:1,typeName:".google.protobuf.ExtensionRangeOptions.VerificationState",defaultValue:"UNVERIFIED",options:{retention:2}}],nestedType:[{name:"Declaration",field:[{name:"number",number:1,type:5,label:1},{name:"full_name",number:2,type:9,label:1},{name:"type",number:3,type:9,label:1},{name:"reserved",number:5,type:8,label:1},{name:"repeated",number:6,type:8,label:1}]}],enumType:[{name:"VerificationState",value:[{name:"DECLARATION",number:0},{name:"UNVERIFIED",number:1}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:3,type:5,label:1},{name:"label",number:4,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Label"},{name:"type",number:5,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Type"},{name:"type_name",number:6,type:9,label:1},{name:"extendee",number:2,type:9,label:1},{name:"default_value",number:7,type:9,label:1},{name:"oneof_index",number:9,type:5,label:1},{name:"json_name",number:10,type:9,label:1},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FieldOptions"},{name:"proto3_optional",number:17,type:8,label:1}],enumType:[{name:"Type",value:[{name:"TYPE_DOUBLE",number:1},{name:"TYPE_FLOAT",number:2},{name:"TYPE_INT64",number:3},{name:"TYPE_UINT64",number:4},{name:"TYPE_INT32",number:5},{name:"TYPE_FIXED64",number:6},{name:"TYPE_FIXED32",number:7},{name:"TYPE_BOOL",number:8},{name:"TYPE_STRING",number:9},{name:"TYPE_GROUP",number:10},{name:"TYPE_MESSAGE",number:11},{name:"TYPE_BYTES",number:12},{name:"TYPE_UINT32",number:13},{name:"TYPE_ENUM",number:14},{name:"TYPE_SFIXED32",number:15},{name:"TYPE_SFIXED64",number:16},{name:"TYPE_SINT32",number:17},{name:"TYPE_SINT64",number:18}]},{name:"Label",value:[{name:"LABEL_OPTIONAL",number:1},{name:"LABEL_REPEATED",number:3},{name:"LABEL_REQUIRED",number:2}]}]},{name:"OneofDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"options",number:2,type:11,label:1,typeName:".google.protobuf.OneofOptions"}]},{name:"EnumDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"value",number:2,type:11,label:3,typeName:".google.protobuf.EnumValueDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumOptions"},{name:"reserved_range",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto.EnumReservedRange"},{name:"reserved_name",number:5,type:9,label:3}],nestedType:[{name:"EnumReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"EnumValueDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumValueOptions"}]},{name:"ServiceDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"method",number:2,type:11,label:3,typeName:".google.protobuf.MethodDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ServiceOptions"}]},{name:"MethodDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"input_type",number:2,type:9,label:1},{name:"output_type",number:3,type:9,label:1},{name:"options",number:4,type:11,label:1,typeName:".google.protobuf.MethodOptions"},{name:"client_streaming",number:5,type:8,label:1,defaultValue:"false"},{name:"server_streaming",number:6,type:8,label:1,defaultValue:"false"}]},{name:"FileOptions",field:[{name:"java_package",number:1,type:9,label:1},{name:"java_outer_classname",number:8,type:9,label:1},{name:"java_multiple_files",number:10,type:8,label:1,defaultValue:"false"},{name:"java_generate_equals_and_hash",number:20,type:8,label:1,options:{deprecated:!0}},{name:"java_string_check_utf8",number:27,type:8,label:1,defaultValue:"false"},{name:"optimize_for",number:9,type:14,label:1,typeName:".google.protobuf.FileOptions.OptimizeMode",defaultValue:"SPEED"},{name:"go_package",number:11,type:9,label:1},{name:"cc_generic_services",number:16,type:8,label:1,defaultValue:"false"},{name:"java_generic_services",number:17,type:8,label:1,defaultValue:"false"},{name:"py_generic_services",number:18,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:23,type:8,label:1,defaultValue:"false"},{name:"cc_enable_arenas",number:31,type:8,label:1,defaultValue:"true"},{name:"objc_class_prefix",number:36,type:9,label:1},{name:"csharp_namespace",number:37,type:9,label:1},{name:"swift_prefix",number:39,type:9,label:1},{name:"php_class_prefix",number:40,type:9,label:1},{name:"php_namespace",number:41,type:9,label:1},{name:"php_metadata_namespace",number:44,type:9,label:1},{name:"ruby_package",number:45,type:9,label:1},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"OptimizeMode",value:[{name:"SPEED",number:1},{name:"CODE_SIZE",number:2},{name:"LITE_RUNTIME",number:3}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"MessageOptions",field:[{name:"message_set_wire_format",number:1,type:8,label:1,defaultValue:"false"},{name:"no_standard_descriptor_accessor",number:2,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"map_entry",number:7,type:8,label:1},{name:"deprecated_legacy_json_field_conflicts",number:11,type:8,label:1,options:{deprecated:!0}},{name:"features",number:12,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldOptions",field:[{name:"ctype",number:1,type:14,label:1,typeName:".google.protobuf.FieldOptions.CType",defaultValue:"STRING"},{name:"packed",number:2,type:8,label:1},{name:"jstype",number:6,type:14,label:1,typeName:".google.protobuf.FieldOptions.JSType",defaultValue:"JS_NORMAL"},{name:"lazy",number:5,type:8,label:1,defaultValue:"false"},{name:"unverified_lazy",number:15,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"weak",number:10,type:8,label:1,defaultValue:"false"},{name:"debug_redact",number:16,type:8,label:1,defaultValue:"false"},{name:"retention",number:17,type:14,label:1,typeName:".google.protobuf.FieldOptions.OptionRetention"},{name:"targets",number:19,type:14,label:3,typeName:".google.protobuf.FieldOptions.OptionTargetType"},{name:"edition_defaults",number:20,type:11,label:3,typeName:".google.protobuf.FieldOptions.EditionDefault"},{name:"features",number:21,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"feature_support",number:22,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],nestedType:[{name:"EditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"value",number:2,type:9,label:1}]},{name:"FeatureSupport",field:[{name:"edition_introduced",number:1,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"edition_deprecated",number:2,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"deprecation_warning",number:3,type:9,label:1},{name:"edition_removed",number:4,type:14,label:1,typeName:".google.protobuf.Edition"}]}],enumType:[{name:"CType",value:[{name:"STRING",number:0},{name:"CORD",number:1},{name:"STRING_PIECE",number:2}]},{name:"JSType",value:[{name:"JS_NORMAL",number:0},{name:"JS_STRING",number:1},{name:"JS_NUMBER",number:2}]},{name:"OptionRetention",value:[{name:"RETENTION_UNKNOWN",number:0},{name:"RETENTION_RUNTIME",number:1},{name:"RETENTION_SOURCE",number:2}]},{name:"OptionTargetType",value:[{name:"TARGET_TYPE_UNKNOWN",number:0},{name:"TARGET_TYPE_FILE",number:1},{name:"TARGET_TYPE_EXTENSION_RANGE",number:2},{name:"TARGET_TYPE_MESSAGE",number:3},{name:"TARGET_TYPE_FIELD",number:4},{name:"TARGET_TYPE_ONEOF",number:5},{name:"TARGET_TYPE_ENUM",number:6},{name:"TARGET_TYPE_ENUM_ENTRY",number:7},{name:"TARGET_TYPE_SERVICE",number:8},{name:"TARGET_TYPE_METHOD",number:9}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"OneofOptions",field:[{name:"features",number:1,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumOptions",field:[{name:"allow_alias",number:2,type:8,label:1},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"deprecated_legacy_json_field_conflicts",number:6,type:8,label:1,options:{deprecated:!0}},{name:"features",number:7,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumValueOptions",field:[{name:"deprecated",number:1,type:8,label:1,defaultValue:"false"},{name:"features",number:2,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"debug_redact",number:3,type:8,label:1,defaultValue:"false"},{name:"feature_support",number:4,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"ServiceOptions",field:[{name:"features",number:34,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"MethodOptions",field:[{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"idempotency_level",number:34,type:14,label:1,typeName:".google.protobuf.MethodOptions.IdempotencyLevel",defaultValue:"IDEMPOTENCY_UNKNOWN"},{name:"features",number:35,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"IdempotencyLevel",value:[{name:"IDEMPOTENCY_UNKNOWN",number:0},{name:"NO_SIDE_EFFECTS",number:1},{name:"IDEMPOTENT",number:2}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"UninterpretedOption",field:[{name:"name",number:2,type:11,label:3,typeName:".google.protobuf.UninterpretedOption.NamePart"},{name:"identifier_value",number:3,type:9,label:1},{name:"positive_int_value",number:4,type:4,label:1},{name:"negative_int_value",number:5,type:3,label:1},{name:"double_value",number:6,type:1,label:1},{name:"string_value",number:7,type:12,label:1},{name:"aggregate_value",number:8,type:9,label:1}],nestedType:[{name:"NamePart",field:[{name:"name_part",number:1,type:9,label:2},{name:"is_extension",number:2,type:8,label:2}]}]},{name:"FeatureSet",field:[{name:"field_presence",number:1,type:14,label:1,typeName:".google.protobuf.FeatureSet.FieldPresence",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPLICIT",edition:900},{value:"IMPLICIT",edition:999},{value:"EXPLICIT",edition:1e3}]}},{name:"enum_type",number:2,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnumType",options:{retention:1,targets:[6,1],editionDefaults:[{value:"CLOSED",edition:900},{value:"OPEN",edition:999}]}},{name:"repeated_field_encoding",number:3,type:14,label:1,typeName:".google.protobuf.FeatureSet.RepeatedFieldEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPANDED",edition:900},{value:"PACKED",edition:999}]}},{name:"utf8_validation",number:4,type:14,label:1,typeName:".google.protobuf.FeatureSet.Utf8Validation",options:{retention:1,targets:[4,1],editionDefaults:[{value:"NONE",edition:900},{value:"VERIFY",edition:999}]}},{name:"message_encoding",number:5,type:14,label:1,typeName:".google.protobuf.FeatureSet.MessageEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"LENGTH_PREFIXED",edition:900}]}},{name:"json_format",number:6,type:14,label:1,typeName:".google.protobuf.FeatureSet.JsonFormat",options:{retention:1,targets:[3,6,1],editionDefaults:[{value:"LEGACY_BEST_EFFORT",edition:900},{value:"ALLOW",edition:999}]}},{name:"enforce_naming_style",number:7,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnforceNamingStyle",options:{retention:2,targets:[1,2,3,4,5,6,7,8,9],editionDefaults:[{value:"STYLE_LEGACY",edition:900},{value:"STYLE2024",edition:1001}]}}],enumType:[{name:"FieldPresence",value:[{name:"FIELD_PRESENCE_UNKNOWN",number:0},{name:"EXPLICIT",number:1},{name:"IMPLICIT",number:2},{name:"LEGACY_REQUIRED",number:3}]},{name:"EnumType",value:[{name:"ENUM_TYPE_UNKNOWN",number:0},{name:"OPEN",number:1},{name:"CLOSED",number:2}]},{name:"RepeatedFieldEncoding",value:[{name:"REPEATED_FIELD_ENCODING_UNKNOWN",number:0},{name:"PACKED",number:1},{name:"EXPANDED",number:2}]},{name:"Utf8Validation",value:[{name:"UTF8_VALIDATION_UNKNOWN",number:0},{name:"VERIFY",number:2},{name:"NONE",number:3}]},{name:"MessageEncoding",value:[{name:"MESSAGE_ENCODING_UNKNOWN",number:0},{name:"LENGTH_PREFIXED",number:1},{name:"DELIMITED",number:2}]},{name:"JsonFormat",value:[{name:"JSON_FORMAT_UNKNOWN",number:0},{name:"ALLOW",number:1},{name:"LEGACY_BEST_EFFORT",number:2}]},{name:"EnforceNamingStyle",value:[{name:"ENFORCE_NAMING_STYLE_UNKNOWN",number:0},{name:"STYLE2024",number:1},{name:"STYLE_LEGACY",number:2}]}],extensionRange:[{start:1e3,end:9995},{start:9995,end:1e4},{start:1e4,end:10001}]},{name:"FeatureSetDefaults",field:[{name:"defaults",number:1,type:11,label:3,typeName:".google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault"},{name:"minimum_edition",number:4,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"maximum_edition",number:5,type:14,label:1,typeName:".google.protobuf.Edition"}],nestedType:[{name:"FeatureSetEditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"overridable_features",number:4,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"fixed_features",number:5,type:11,label:1,typeName:".google.protobuf.FeatureSet"}]}]},{name:"SourceCodeInfo",field:[{name:"location",number:1,type:11,label:3,typeName:".google.protobuf.SourceCodeInfo.Location"}],nestedType:[{name:"Location",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"span",number:2,type:5,label:3,options:{packed:!0}},{name:"leading_comments",number:3,type:9,label:1},{name:"trailing_comments",number:4,type:9,label:1},{name:"leading_detached_comments",number:6,type:9,label:3}]}],extensionRange:[{start:536e6,end:536000001}]},{name:"GeneratedCodeInfo",field:[{name:"annotation",number:1,type:11,label:3,typeName:".google.protobuf.GeneratedCodeInfo.Annotation"}],nestedType:[{name:"Annotation",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"source_file",number:2,type:9,label:1},{name:"begin",number:3,type:5,label:1},{name:"end",number:4,type:5,label:1},{name:"semantic",number:5,type:14,label:1,typeName:".google.protobuf.GeneratedCodeInfo.Annotation.Semantic"}],enumType:[{name:"Semantic",value:[{name:"NONE",number:0},{name:"SET",number:1},{name:"ALIAS",number:2}]}]}]}],enumType:[{name:"Edition",value:[{name:"EDITION_UNKNOWN",number:0},{name:"EDITION_LEGACY",number:900},{name:"EDITION_PROTO2",number:998},{name:"EDITION_PROTO3",number:999},{name:"EDITION_2023",number:1e3},{name:"EDITION_2024",number:1001},{name:"EDITION_1_TEST_ONLY",number:1},{name:"EDITION_2_TEST_ONLY",number:2},{name:"EDITION_99997_TEST_ONLY",number:99997},{name:"EDITION_99998_TEST_ONLY",number:99998},{name:"EDITION_99999_TEST_ONLY",number:99999},{name:"EDITION_MAX",number:2147483647}]}]}),1);var Hs,Gs,Vs,Bs,Ys,js,Ks,Ws,zs,Xs,Js,Zs,Qs,ea,ta,na,sa,aa;(function(e){e[e.DECLARATION=0]="DECLARATION",e[e.UNVERIFIED=1]="UNVERIFIED"})(Hs||(Hs={})),function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.GROUP=10]="GROUP",e[e.MESSAGE=11]="MESSAGE",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.ENUM=14]="ENUM",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"}(Gs||(Gs={})),function(e){e[e.OPTIONAL=1]="OPTIONAL",e[e.REPEATED=3]="REPEATED",e[e.REQUIRED=2]="REQUIRED"}(Vs||(Vs={})),function(e){e[e.SPEED=1]="SPEED",e[e.CODE_SIZE=2]="CODE_SIZE",e[e.LITE_RUNTIME=3]="LITE_RUNTIME"}(Bs||(Bs={})),function(e){e[e.STRING=0]="STRING",e[e.CORD=1]="CORD",e[e.STRING_PIECE=2]="STRING_PIECE"}(Ys||(Ys={})),function(e){e[e.JS_NORMAL=0]="JS_NORMAL",e[e.JS_STRING=1]="JS_STRING",e[e.JS_NUMBER=2]="JS_NUMBER"}(js||(js={})),function(e){e[e.RETENTION_UNKNOWN=0]="RETENTION_UNKNOWN",e[e.RETENTION_RUNTIME=1]="RETENTION_RUNTIME",e[e.RETENTION_SOURCE=2]="RETENTION_SOURCE"}(Ks||(Ks={})),function(e){e[e.TARGET_TYPE_UNKNOWN=0]="TARGET_TYPE_UNKNOWN",e[e.TARGET_TYPE_FILE=1]="TARGET_TYPE_FILE",e[e.TARGET_TYPE_EXTENSION_RANGE=2]="TARGET_TYPE_EXTENSION_RANGE",e[e.TARGET_TYPE_MESSAGE=3]="TARGET_TYPE_MESSAGE",e[e.TARGET_TYPE_FIELD=4]="TARGET_TYPE_FIELD",e[e.TARGET_TYPE_ONEOF=5]="TARGET_TYPE_ONEOF",e[e.TARGET_TYPE_ENUM=6]="TARGET_TYPE_ENUM",e[e.TARGET_TYPE_ENUM_ENTRY=7]="TARGET_TYPE_ENUM_ENTRY",e[e.TARGET_TYPE_SERVICE=8]="TARGET_TYPE_SERVICE",e[e.TARGET_TYPE_METHOD=9]="TARGET_TYPE_METHOD"}(Ws||(Ws={})),function(e){e[e.IDEMPOTENCY_UNKNOWN=0]="IDEMPOTENCY_UNKNOWN",e[e.NO_SIDE_EFFECTS=1]="NO_SIDE_EFFECTS",e[e.IDEMPOTENT=2]="IDEMPOTENT"}(zs||(zs={})),function(e){e[e.FIELD_PRESENCE_UNKNOWN=0]="FIELD_PRESENCE_UNKNOWN",e[e.EXPLICIT=1]="EXPLICIT",e[e.IMPLICIT=2]="IMPLICIT",e[e.LEGACY_REQUIRED=3]="LEGACY_REQUIRED"}(Xs||(Xs={})),function(e){e[e.ENUM_TYPE_UNKNOWN=0]="ENUM_TYPE_UNKNOWN",e[e.OPEN=1]="OPEN",e[e.CLOSED=2]="CLOSED"}(Js||(Js={})),function(e){e[e.REPEATED_FIELD_ENCODING_UNKNOWN=0]="REPEATED_FIELD_ENCODING_UNKNOWN",e[e.PACKED=1]="PACKED",e[e.EXPANDED=2]="EXPANDED"}(Zs||(Zs={})),function(e){e[e.UTF8_VALIDATION_UNKNOWN=0]="UTF8_VALIDATION_UNKNOWN",e[e.VERIFY=2]="VERIFY",e[e.NONE=3]="NONE"}(Qs||(Qs={})),function(e){e[e.MESSAGE_ENCODING_UNKNOWN=0]="MESSAGE_ENCODING_UNKNOWN",e[e.LENGTH_PREFIXED=1]="LENGTH_PREFIXED",e[e.DELIMITED=2]="DELIMITED"}(ea||(ea={})),function(e){e[e.JSON_FORMAT_UNKNOWN=0]="JSON_FORMAT_UNKNOWN",e[e.ALLOW=1]="ALLOW",e[e.LEGACY_BEST_EFFORT=2]="LEGACY_BEST_EFFORT"}(ta||(ta={})),function(e){e[e.ENFORCE_NAMING_STYLE_UNKNOWN=0]="ENFORCE_NAMING_STYLE_UNKNOWN",e[e.STYLE2024=1]="STYLE2024",e[e.STYLE_LEGACY=2]="STYLE_LEGACY"}(na||(na={})),function(e){e[e.NONE=0]="NONE",e[e.SET=1]="SET",e[e.ALIAS=2]="ALIAS"}(sa||(sa={})),function(e){e[e.EDITION_UNKNOWN=0]="EDITION_UNKNOWN",e[e.EDITION_LEGACY=900]="EDITION_LEGACY",e[e.EDITION_PROTO2=998]="EDITION_PROTO2",e[e.EDITION_PROTO3=999]="EDITION_PROTO3",e[e.EDITION_2023=1e3]="EDITION_2023",e[e.EDITION_2024=1001]="EDITION_2024",e[e.EDITION_1_TEST_ONLY=1]="EDITION_1_TEST_ONLY",e[e.EDITION_2_TEST_ONLY=2]="EDITION_2_TEST_ONLY",e[e.EDITION_99997_TEST_ONLY=99997]="EDITION_99997_TEST_ONLY",e[e.EDITION_99998_TEST_ONLY=99998]="EDITION_99998_TEST_ONLY",e[e.EDITION_99999_TEST_ONLY=99999]="EDITION_99999_TEST_ONLY",e[e.EDITION_MAX=2147483647]="EDITION_MAX"}(aa||(aa={}));const li={readUnknownFields:!0};function ms(e,t,n){const s=le(e,void 0,!1);return Tr(s,new rs(t),li,!1,t.byteLength),s.message}function Tr(e,t,n,s,a){var r;const o=s?t.len:t.pos+a;let i,l;const u=(r=e.getUnknown())!==null&&r!==void 0?r:[];for(;t.pos<o&&([i,l]=t.tag(),!s||l!=F.EndGroup);){const m=e.findNumber(i);if(m)Er(e,t,m,l,n);else{const d=t.skip(l,i);n.readUnknownFields&&u.push({no:i,wireType:l,data:d})}}if(s&&(l!=F.EndGroup||i!==a))throw new Error("invalid end group tag");u.length>0&&e.setUnknown(u)}function Er(e,t,n,s,a){switch(n.fieldKind){case"scalar":e.set(n,We(t,n.scalar));break;case"enum":e.set(n,We(t,p.INT32));break;case"message":e.set(n,Tn(t,a,n,e.get(n)));break;case"list":(function(r,o,i,l){var u;const m=i.field();if(m.listKind==="message")return void i.add(Tn(r,l,m));const d=(u=m.scalar)!==null&&u!==void 0?u:p.INT32;if(!(o==F.LengthDelimited&&d!=p.STRING&&d!=p.BYTES))return void i.add(We(r,d));const g=r.uint32()+r.pos;for(;r.pos<g;)i.add(We(r,d))})(t,s,e.get(n),a);break;case"map":(function(r,o,i){const l=o.field();let u,m;const d=r.pos+r.uint32();for(;r.pos<d;){const[h]=r.tag();switch(h){case 1:u=We(r,l.mapKey);break;case 2:switch(l.mapKind){case"scalar":m=We(r,l.scalar);break;case"enum":m=r.int32();break;case"message":m=Tn(r,i,l)}}}if(u===void 0&&(u=Ve(l.mapKey,!1)),m===void 0)switch(l.mapKind){case"scalar":m=Ve(l.scalar,!1);break;case"enum":m=l.enum.values[0].number;break;case"message":m=le(l.message,void 0,!1)}o.set(u,m)})(t,e.get(n),a)}}function Tn(e,t,n,s){const a=n.delimitedEncoding,r=s??le(n.message,void 0,!1);return Tr(r,e,t,a,a?n.number:e.uint32()),r}function We(e,t){switch(t){case p.STRING:return e.string();case p.BOOL:return e.bool();case p.DOUBLE:return e.double();case p.FLOAT:return e.float();case p.INT32:return e.int32();case p.INT64:return e.int64();case p.UINT64:return e.uint64();case p.FIXED64:return e.fixed64();case p.BYTES:return e.bytes();case p.FIXED32:return e.fixed32();case p.SFIXED32:return e.sfixed32();case p.SFIXED64:return e.sfixed64();case p.SINT64:return e.sint64();case p.UINT32:return e.uint32();case p.SINT32:return e.sint32()}}function hs(e,t){const n=ms(ii,hr(e));return n.messageType.forEach(ds),n.dependency=[],gr(n,s=>{}).getFile(n.name)}const ui=Et(hs("Chlnb29nbGUvcHJvdG9idWYvYW55LnByb3RvEg9nb29nbGUucHJvdG9idWYiJgoDQW55EhAKCHR5cGVfdXJsGAEgASgJEg0KBXZhbHVlGAIgASgMQnYKE2NvbS5nb29nbGUucHJvdG9idWZCCEFueVByb3RvUAFaLGdvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL2FueXBiogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),0),ci=3,ra={writeUnknownFields:!0};function di(e,t,n){return jt(new sr,function(s){return s?Object.assign(Object.assign({},ra),s):ra}(n),le(e,t)).finish()}function jt(e,t,n){var s;for(const a of n.sortedFields)if(n.isSet(a))Sr(e,t,n,a);else if(a.presence==ci)throw new Error(`cannot encode ${a} to binary: required field not set`);if(t.writeUnknownFields)for(const{no:a,wireType:r,data:o}of(s=n.getUnknown())!==null&&s!==void 0?s:[])e.tag(a,r).raw(o);return e}function Sr(e,t,n,s){var a;switch(s.fieldKind){case"scalar":case"enum":Kt(e,n.desc.typeName,s.name,(a=s.scalar)!==null&&a!==void 0?a:p.INT32,s.number,n.get(s));break;case"list":(function(r,o,i,l){var u;if(i.listKind=="message"){for(const d of l)oa(r,o,i,d);return}const m=(u=i.scalar)!==null&&u!==void 0?u:p.INT32;if(i.packed){if(!l.size)return;r.tag(i.number,F.LengthDelimited).fork();for(const d of l)Ir(r,i.parent.typeName,i.name,m,d);return void r.join()}for(const d of l)Kt(r,i.parent.typeName,i.name,m,i.number,d)})(e,t,s,n.get(s));break;case"message":oa(e,t,s,n.get(s));break;case"map":for(const[r,o]of n.get(s))mi(e,t,s,r,o)}}function Kt(e,t,n,s,a,r){Ir(e.tag(a,function(o){switch(o){case p.BYTES:case p.STRING:return F.LengthDelimited;case p.DOUBLE:case p.FIXED64:case p.SFIXED64:return F.Bit64;case p.FIXED32:case p.SFIXED32:case p.FLOAT:return F.Bit32;default:return F.Varint}}(s)),t,n,s,r)}function oa(e,t,n,s){n.delimitedEncoding?jt(e.tag(n.number,F.StartGroup),t,s).tag(n.number,F.EndGroup):jt(e.tag(n.number,F.LengthDelimited).fork(),t,s).join()}function mi(e,t,n,s,a){var r;switch(e.tag(n.number,F.LengthDelimited).fork(),Kt(e,n.parent.typeName,n.name,n.mapKey,1,s),n.mapKind){case"scalar":case"enum":Kt(e,n.parent.typeName,n.name,(r=n.scalar)!==null&&r!==void 0?r:p.INT32,2,a);break;case"message":jt(e.tag(2,F.LengthDelimited).fork(),t,a).join()}e.join()}function Ir(e,t,n,s,a){try{switch(s){case p.STRING:e.string(a);break;case p.BOOL:e.bool(a);break;case p.DOUBLE:e.double(a);break;case p.FLOAT:e.float(a);break;case p.INT32:e.int32(a);break;case p.INT64:e.int64(a);break;case p.UINT64:e.uint64(a);break;case p.FIXED64:e.fixed64(a);break;case p.BYTES:e.bytes(a);break;case p.FIXED32:e.fixed32(a);break;case p.SFIXED32:e.sfixed32(a);break;case p.SFIXED64:e.sfixed64(a);break;case p.SINT64:e.sint64(a);break;case p.UINT32:e.uint32(a);break;case p.SINT32:e.sint32(a)}}catch(r){throw r instanceof Error?new Error(`cannot encode field ${t}.${n} to binary: ${r.message}`):r}}function hi(e,t){if(e.typeUrl==="")return;const n=t.kind=="message"?t:t.getMessage(ia(e.typeUrl));return n&&function(s,a){return s.typeUrl!==""&&(typeof a=="string"?a:a.typeName)===ia(s.typeUrl)}(e,n)?ms(n,e.value):void 0}function ia(e){const t=e.lastIndexOf("/"),n=t>=0?e.substring(t+1):e;if(!n.length)throw new Error(`invalid type url: ${e}`);return n}const ps=hs("Chxnb29nbGUvcHJvdG9idWYvc3RydWN0LnByb3RvEg9nb29nbGUucHJvdG9idWYihAEKBlN0cnVjdBIzCgZmaWVsZHMYASADKAsyIy5nb29nbGUucHJvdG9idWYuU3RydWN0LkZpZWxkc0VudHJ5GkUKC0ZpZWxkc0VudHJ5EgsKA2tleRgBIAEoCRIlCgV2YWx1ZRgCIAEoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZToCOAEi6gEKBVZhbHVlEjAKCm51bGxfdmFsdWUYASABKA4yGi5nb29nbGUucHJvdG9idWYuTnVsbFZhbHVlSAASFgoMbnVtYmVyX3ZhbHVlGAIgASgBSAASFgoMc3RyaW5nX3ZhbHVlGAMgASgJSAASFAoKYm9vbF92YWx1ZRgEIAEoCEgAEi8KDHN0cnVjdF92YWx1ZRgFIAEoCzIXLmdvb2dsZS5wcm90b2J1Zi5TdHJ1Y3RIABIwCgpsaXN0X3ZhbHVlGAYgASgLMhouZ29vZ2xlLnByb3RvYnVmLkxpc3RWYWx1ZUgAQgYKBGtpbmQiMwoJTGlzdFZhbHVlEiYKBnZhbHVlcxgBIAMoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZSobCglOdWxsVmFsdWUSDgoKTlVMTF9WQUxVRRAAQn8KE2NvbS5nb29nbGUucHJvdG9idWZCC1N0cnVjdFByb3RvUAFaL2dvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL3N0cnVjdHBi+AEBogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),pi=Et(ps,0),Nr=Et(ps,1),gi=Et(ps,2);var Bn;function fi(e,t){wr(t,e);const n=function(o,i){if(o===void 0)return[];if(i.fieldKind==="enum"||i.fieldKind==="scalar"){for(let l=o.length-1;l>=0;--l)if(o[l].no==i.number)return[o[l]];return[]}return o.filter(l=>l.no===i.number)}(e.$unknown,t),[s,a,r]=cn(t);for(const o of n)Er(s,new rs(o.data),a,o.wireType,{readUnknownFields:!0});return r()}function yi(e,t,n){var s;wr(t,e);const a=((s=e.$unknown)!==null&&s!==void 0?s:[]).filter(u=>u.no!==t.number),[r,o]=cn(t,n),i=new sr;Sr(i,{writeUnknownFields:!0},r,o);const l=new rs(i.finish());for(;l.pos<l.len;){const[u,m]=l.tag(),d=l.skip(m,u);a.push({no:u,wireType:m,data:d})}e.$unknown=a}function cn(e,t){const n=e.typeName,s=Object.assign(Object.assign({},e),{kind:"field",parent:e.extendee,localName:n}),a=Object.assign(Object.assign({},e.extendee),{fields:[s],members:[s],oneofs:[]}),r=fe(a,t!==void 0?{[n]:t}:void 0);return[le(a,r),s,()=>{const o=r[n];if(o===void 0){const i=e.message;return Tt(i)?Ve(i.fields[0].scalar,i.fields[0].longAsString):fe(i)}return o}]}function wr(e,t){if(e.extendee.typeName!=t.$typeName)throw new Error(`extension ${e.typeName} can only be applied to message ${e.extendee.typeName}`)}(function(e){e[e.NULL_VALUE=0]="NULL_VALUE"})(Bn||(Bn={}));const bi=3,_i=2,la={alwaysEmitImplicit:!1,enumAsInteger:!1,useProtoFieldName:!1};function vi(e,t,n){return pt(le(e,t),function(s){return s?Object.assign(Object.assign({},la),s):la}(n))}function pt(e,t){var n;const s=function(r,o){if(r.desc.typeName.startsWith("google.protobuf.")){switch(r.desc.typeName){case"google.protobuf.Any":return function(l,u){if(l.typeUrl==="")return{};const{registry:m}=u;let d,h;if(m&&(d=hi(l,m),d&&(h=m.getMessage(d.$typeName))),!h||!d)throw new Error(`cannot encode message ${l.$typeName} to JSON: "${l.typeUrl}" is not in the type registry`);let g=pt(le(h,d),u);return(h.typeName.startsWith("google.protobuf.")||g===null||Array.isArray(g)||typeof g!="object")&&(g={value:g}),g["@type"]=l.typeUrl,g}(r.message,o);case"google.protobuf.Timestamp":return function(l){const u=1e3*Number(l.seconds);if(u<Date.parse("0001-01-01T00:00:00Z")||u>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot encode message ${l.$typeName} to JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);if(l.nanos<0)throw new Error(`cannot encode message ${l.$typeName} to JSON: nanos must not be negative`);let m="Z";if(l.nanos>0){const d=(l.nanos+1e9).toString().substring(1);m=d.substring(3)==="000000"?"."+d.substring(0,3)+"Z":d.substring(6)==="000"?"."+d.substring(0,6)+"Z":"."+d+"Z"}return new Date(u).toISOString().replace(".000Z",m)}(r.message);case"google.protobuf.Duration":return function(l){if(Number(l.seconds)>315576e6||Number(l.seconds)<-315576e6)throw new Error(`cannot encode message ${l.$typeName} to JSON: value out of range`);let u=l.seconds.toString();if(l.nanos!==0){let m=Math.abs(l.nanos).toString();m="0".repeat(9-m.length)+m,m.substring(3)==="000000"?m=m.substring(0,3):m.substring(6)==="000"&&(m=m.substring(0,6)),u+="."+m,l.nanos<0&&Number(l.seconds)==0&&(u="-"+u)}return u+"s"}(r.message);case"google.protobuf.FieldMask":return(i=r.message).paths.map(l=>{if(l.match(/_[0-9]?_/g)||l.match(/[A-Z]/g))throw new Error(`cannot encode message ${i.$typeName} to JSON: lowerCamelCase of path name "`+l+'" is irreversible');return yt(l)}).join(",");case"google.protobuf.Struct":return Cr(r.message);case"google.protobuf.Value":return gs(r.message);case"google.protobuf.ListValue":return kr(r.message);default:if(Tt(r.desc)){const l=r.desc.fields[0];return Pt(l,r.get(l))}return}var i}}(e,t);if(s!==void 0)return s;const a={};for(const r of e.sortedFields){if(!e.isSet(r)){if(r.presence==bi)throw new Error(`cannot encode ${r} to JSON: required field not set`);if(!t.alwaysEmitImplicit||r.presence!==_i)continue}const o=ua(r,e.get(r),t);o!==void 0&&(a[Ti(r,t)]=o)}if(t.registry){const r=new Set;for(const{no:o}of(n=e.getUnknown())!==null&&n!==void 0?n:[])if(!r.has(o)){r.add(o);const i=t.registry.getExtensionFor(e.desc,o);if(!i)continue;const l=fi(e.message,i),[u,m]=cn(i,l),d=ua(m,u.get(m),t);d!==void 0&&(a[i.jsonName]=d)}}return a}function ua(e,t,n){switch(e.fieldKind){case"scalar":return Pt(e,t);case"message":return pt(t,n);case"enum":return En(e.enum,t,n.enumAsInteger);case"list":return function(s,a){const r=s.field(),o=[];switch(r.listKind){case"scalar":for(const i of s)o.push(Pt(r,i));break;case"enum":for(const i of s)o.push(En(r.enum,i,a.enumAsInteger));break;case"message":for(const i of s)o.push(pt(i,a))}return a.alwaysEmitImplicit||o.length>0?o:void 0}(t,n);case"map":return function(s,a){const r=s.field(),o={};switch(r.mapKind){case"scalar":for(const[i,l]of s)o[i]=Pt(r,l);break;case"message":for(const[i,l]of s)o[i]=pt(l,a);break;case"enum":for(const[i,l]of s)o[i]=En(r.enum,l,a.enumAsInteger)}return a.alwaysEmitImplicit||s.size>0?o:void 0}(t,n)}}function En(e,t,n){var s;if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: expected number, got ${$(t)}`);if(e.typeName=="google.protobuf.NullValue")return null;if(n)return t;const a=e.value[t];return(s=a==null?void 0:a.name)!==null&&s!==void 0?s:t}function Pt(e,t){var n,s,a,r,o,i;switch(e.scalar){case p.INT32:case p.SFIXED32:case p.SINT32:case p.FIXED32:case p.UINT32:if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: ${(n=Le(e,t))===null||n===void 0?void 0:n.message}`);return t;case p.FLOAT:case p.DOUBLE:if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: ${(s=Le(e,t))===null||s===void 0?void 0:s.message}`);return Number.isNaN(t)?"NaN":t===Number.POSITIVE_INFINITY?"Infinity":t===Number.NEGATIVE_INFINITY?"-Infinity":t;case p.STRING:if(typeof t!="string")throw new Error(`cannot encode ${e} to JSON: ${(a=Le(e,t))===null||a===void 0?void 0:a.message}`);return t;case p.BOOL:if(typeof t!="boolean")throw new Error(`cannot encode ${e} to JSON: ${(r=Le(e,t))===null||r===void 0?void 0:r.message}`);return t;case p.UINT64:case p.FIXED64:case p.INT64:case p.SFIXED64:case p.SINT64:if(typeof t!="bigint"&&typeof t!="string")throw new Error(`cannot encode ${e} to JSON: ${(o=Le(e,t))===null||o===void 0?void 0:o.message}`);return t.toString();case p.BYTES:if(t instanceof Uint8Array)return function(l,u="std"){const m=pr(u),d=u=="std";let h,g="",f=0,b=0;for(let y=0;y<l.length;y++)switch(h=l[y],f){case 0:g+=m[h>>2],b=(3&h)<<4,f=1;break;case 1:g+=m[b|h>>4],b=(15&h)<<2,f=2;break;case 2:g+=m[b|h>>6],g+=m[63&h],f=0}return f&&(g+=m[b],d&&(g+="=",f==1&&(g+="="))),g}(t);throw new Error(`cannot encode ${e} to JSON: ${(i=Le(e,t))===null||i===void 0?void 0:i.message}`)}}function Ti(e,t){return t.useProtoFieldName?e.name:e.jsonName}function Cr(e){const t={};for(const[n,s]of Object.entries(e.fields))t[n]=gs(s);return t}function gs(e){switch(e.kind.case){case"nullValue":return null;case"numberValue":if(!Number.isFinite(e.kind.value))throw new Error(`${e.$typeName} cannot be NaN or Infinity`);return e.kind.value;case"boolValue":case"stringValue":return e.kind.value;case"structValue":return Cr(e.kind.value);case"listValue":return kr(e.kind.value);default:throw new Error(`${e.$typeName} must have a value`)}}function kr(e){return e.values.map(gs)}const ca={ignoreUnknownFields:!1};function Ei(e,t,n){const s=le(e);try{nt(s,t,function(r){return r?Object.assign(Object.assign({},ca),r):ca}(n))}catch(r){throw(a=r)instanceof Error&&Fo.includes(a.name)&&"field"in a&&typeof a.field=="function"?new Error(`cannot decode ${r.field()} from JSON: ${r.message}`,{cause:r}):r}var a;return s.message}function nt(e,t,n){var s;if(function(o,i,l){if(!o.desc.typeName.startsWith("google.protobuf."))return!1;switch(o.desc.typeName){case"google.protobuf.Any":return function(u,m,d){var h;if(m===null||Array.isArray(m)||typeof m!="object")throw new Error(`cannot decode message ${u.$typeName} from JSON: expected object but got ${$(m)}`);if(Object.keys(m).length==0)return;const g=m["@type"];if(typeof g!="string"||g=="")throw new Error(`cannot decode message ${u.$typeName} from JSON: "@type" is empty`);const f=g.includes("/")?g.substring(g.lastIndexOf("/")+1):g;if(!f.length)throw new Error(`cannot decode message ${u.$typeName} from JSON: "@type" is invalid`);const b=(h=d.registry)===null||h===void 0?void 0:h.getMessage(f);if(!b)throw new Error(`cannot decode message ${u.$typeName} from JSON: ${g} is not in the type registry`);const y=le(b);if(f.startsWith("google.protobuf.")&&Object.prototype.hasOwnProperty.call(m,"value"))nt(y,m.value,d);else{const v=Object.assign({},m);delete v["@type"],nt(y,v,d)}(function(v,C,N){let P=!1;N||(N=fe(ui),P=!0),N.value=di(v,C),N.typeUrl=`type.googleapis.com/${C.$typeName}`})(y.desc,y.message,u)}(o.message,i,l),!0;case"google.protobuf.Timestamp":return function(u,m){if(typeof m!="string")throw new Error(`cannot decode message ${u.$typeName} from JSON: ${$(m)}`);const d=m.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:\.([0-9]{1,9}))?(?:Z|([+-][0-9][0-9]:[0-9][0-9]))$/);if(!d)throw new Error(`cannot decode message ${u.$typeName} from JSON: invalid RFC 3339 string`);const h=Date.parse(d[1]+"-"+d[2]+"-"+d[3]+"T"+d[4]+":"+d[5]+":"+d[6]+(d[8]?d[8]:"Z"));if(Number.isNaN(h))throw new Error(`cannot decode message ${u.$typeName} from JSON: invalid RFC 3339 string`);if(h<Date.parse("0001-01-01T00:00:00Z")||h>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot decode message ${u.$typeName} from JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);u.seconds=O.parse(h/1e3),u.nanos=0,d[7]&&(u.nanos=parseInt("1"+d[7]+"0".repeat(9-d[7].length))-1e9)}(o.message,i),!0;case"google.protobuf.Duration":return function(u,m){if(typeof m!="string")throw new Error(`cannot decode message ${u.$typeName} from JSON: ${$(m)}`);const d=m.match(/^(-?[0-9]+)(?:\.([0-9]+))?s/);if(d===null)throw new Error(`cannot decode message ${u.$typeName} from JSON: ${$(m)}`);const h=Number(d[1]);if(h>315576e6||h<-315576e6)throw new Error(`cannot decode message ${u.$typeName} from JSON: ${$(m)}`);if(u.seconds=O.parse(h),typeof d[2]!="string")return;const g=d[2]+"0".repeat(9-d[2].length);u.nanos=parseInt(g),(h<0||Object.is(h,-0))&&(u.nanos=-u.nanos)}(o.message,i),!0;case"google.protobuf.FieldMask":return function(u,m){if(typeof m!="string")throw new Error(`cannot decode message ${u.$typeName} from JSON: ${$(m)}`);if(m==="")return;function d(h){if(h.includes("_"))throw new Error(`cannot decode message ${u.$typeName} from JSON: path names must be lowerCamelCase`);const g=h.replace(/[A-Z]/g,f=>"_"+f.toLowerCase());return g[0]==="_"?g.substring(1):g}u.paths=m.split(",").map(d)}(o.message,i),!0;case"google.protobuf.Struct":return xr(o.message,i),!0;case"google.protobuf.Value":return fs(o.message,i),!0;case"google.protobuf.ListValue":return Ar(o.message,i),!0;default:if(Tt(o.desc)){const u=o.desc.fields[0];return i===null?o.clear(u):o.set(u,$t(u,i,!0)),!0}return!1}}(e,t,n))return;if(t==null||Array.isArray(t)||typeof t!="object")throw new Error(`cannot decode ${e.desc} from JSON: ${$(t)}`);const a=new Map,r=new Map;for(const o of e.desc.fields)r.set(o.name,o).set(o.jsonName,o);for(const[o,i]of Object.entries(t)){const l=r.get(o);if(l){if(l.oneof){if(i===null&&l.fieldKind=="scalar")continue;const u=a.get(l.oneof);if(u!==void 0)throw new ee(l.oneof,`oneof set multiple times by ${u.name} and ${l.name}`);a.set(l.oneof,l)}da(e,l,i,n)}else{let u;if(o.startsWith("[")&&o.endsWith("]")&&(u=(s=n.registry)===null||s===void 0?void 0:s.getExtension(o.substring(1,o.length-1)))&&u.extendee.typeName===e.desc.typeName){const[m,d,h]=cn(u);da(m,d,i,n),yi(e.message,u,h())}if(!u&&!n.ignoreUnknownFields)throw new Error(`cannot decode ${e.desc} from JSON: key "${o}" is unknown`)}}}function da(e,t,n,s){switch(t.fieldKind){case"scalar":(function(a,r,o){const i=$t(r,o,!1);i===Wt?a.clear(r):a.set(r,i)})(e,t,n);break;case"enum":(function(a,r,o,i){const l=Sn(r.enum,o,i.ignoreUnknownFields,!1);l===Wt?a.clear(r):l!==Lt&&a.set(r,l)})(e,t,n,s);break;case"message":(function(a,r,o,i){if(o===null&&r.message.typeName!="google.protobuf.Value")return void a.clear(r);const l=a.isSet(r)?a.get(r):le(r.message);nt(l,o,i),a.set(r,l)})(e,t,n,s);break;case"list":(function(a,r,o){if(r===null)return;const i=a.field();if(!Array.isArray(r))throw new ee(i,"expected Array, got "+$(r));for(const l of r){if(l===null)throw new ee(i,"list item must not be null");switch(i.listKind){case"message":const u=le(i.message);nt(u,l,o),a.add(u);break;case"enum":const m=Sn(i.enum,l,o.ignoreUnknownFields,!0);m!==Lt&&a.add(m);break;case"scalar":a.add($t(i,l,!0))}}})(e.get(t),n,s);break;case"map":(function(a,r,o){if(r===null)return;const i=a.field();if(typeof r!="object"||Array.isArray(r))throw new ee(i,"expected object, got "+$(r));for(const[l,u]of Object.entries(r)){if(u===null)throw new ee(i,"map value must not be null");let m;switch(i.mapKind){case"message":const h=le(i.message);nt(h,u,o),m=h;break;case"enum":if(m=Sn(i.enum,u,o.ignoreUnknownFields,!0),m===Lt)return;break;case"scalar":m=$t(i,u,!0)}const d=Si(i.mapKey,l);a.set(d,m)}})(e.get(t),n,s)}}const Lt=Symbol();function Sn(e,t,n,s){if(t===null)return e.typeName=="google.protobuf.NullValue"?0:s?e.values[0].number:Wt;switch(typeof t){case"number":if(Number.isInteger(t))return t;break;case"string":const a=e.values.find(r=>r.name===t);if(a!==void 0)return a.number;if(n)return Lt}throw new Error(`cannot decode ${e} from JSON: ${$(t)}`)}const Wt=Symbol();function $t(e,t,n){if(t===null)return n?Ve(e.scalar,!1):Wt;switch(e.scalar){case p.DOUBLE:case p.FLOAT:if(t==="NaN")return NaN;if(t==="Infinity")return Number.POSITIVE_INFINITY;if(t==="-Infinity")return Number.NEGATIVE_INFINITY;if(typeof t=="number"){if(Number.isNaN(t))throw new ee(e,"unexpected NaN number");if(!Number.isFinite(t))throw new ee(e,"unexpected infinite number");break}if(typeof t=="string"){if(t===""||t.trim().length!==t.length)break;const s=Number(t);if(!Number.isFinite(s))break;return s}break;case p.INT32:case p.FIXED32:case p.SFIXED32:case p.SINT32:case p.UINT32:return Rr(t);case p.BYTES:if(typeof t=="string"){if(t==="")return new Uint8Array(0);try{return hr(t)}catch(s){const a=s instanceof Error?s.message:String(s);throw new ee(e,a)}}}return t}function Si(e,t){switch(e){case p.BOOL:switch(t){case"true":return!0;case"false":return!1}return t;case p.INT32:case p.FIXED32:case p.UINT32:case p.SFIXED32:case p.SINT32:return Rr(t);default:return t}}function Rr(e){if(typeof e=="string"){if(e===""||e.trim().length!==e.length)return e;const t=Number(e);return Number.isNaN(t)?e:t}return e}function xr(e,t){if(typeof t!="object"||t==null||Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${$(t)}`);for(const[n,s]of Object.entries(t)){const a=fe(Nr);fs(a,s),e.fields[n]=a}}function fs(e,t){switch(typeof t){case"number":e.kind={case:"numberValue",value:t};break;case"string":e.kind={case:"stringValue",value:t};break;case"boolean":e.kind={case:"boolValue",value:t};break;case"object":if(t===null)e.kind={case:"nullValue",value:Bn.NULL_VALUE};else if(Array.isArray(t)){const n=fe(gi);Ar(n,t),e.kind={case:"listValue",value:n}}else{const n=fe(pi);xr(n,t),e.kind={case:"structValue",value:n}}break;default:throw new Error(`cannot decode message ${e.$typeName} from JSON ${$(t)}`)}return e}function Ar(e,t){if(!Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${$(t)}`);for(const n of t){const s=fe(Nr);fs(s,n),e.values.push(s)}}class Or{constructor(t){c(this,"target");c(this,"pendingRequests",new Map);c(this,"cleanup");c(this,"serviceRegistries",new Set);this.target=t,this.cleanup=this.target.onReceiveMessage(this.handleMessage.bind(this))}addServiceRegistry(t){this.serviceRegistries.add(t)}removeServiceRegistry(t){this.serviceRegistries.delete(t)}handleMessage(t){if(!t||typeof t!="object"||!this.isGrpcMessageLike(t))return;const n=t;n.type==="com.augmentcode.client.rpc.request"?this.handleRequest(n):n.type==="com.augmentcode.client.rpc.response"&&this.handleResponse(n)}isGrpcMessageLike(t){return"type"in t&&t.type==="com.augmentcode.client.rpc.request"||t.type==="com.augmentcode.client.rpc.response"}async handleRequest(t){for(const n of this.serviceRegistries)if(n.canHandle(t))try{return void await n.handleRequest(t,s=>{this.target.sendMessage(s)})}catch(s){Array.from(this.serviceRegistries).indexOf(n)===this.serviceRegistries.size-1&&this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:s instanceof Error?s.message:String(s)})}this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:`No handlers registered for service: ${t.serviceTypeName}`})}handleResponse(t){const n=this.pendingRequests.get(t.id);if(n)if(this.pendingRequests.delete(t.id),clearTimeout(n.timeout),t.error)n.reject(new Error(`gRPC server error for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${t.error}`));else try{if(!t.data&&t.data!==null&&t.data!=="")throw new Error(`gRPC response missing data field for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id})`);n.resolve(t)}catch(s){const a=s instanceof Error?s.message:String(s);n.reject(new Error(`Failed to process gRPC response for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${a}`))}}sendRequest(t,n){return new Promise((s,a)=>{let r;n&&(r=setTimeout(()=>{this.pendingRequests.delete(t.id),a(new Error(`gRPC request timed out after ${n}ms: ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}). This may indicate that the server is not responding or the message routing is broken.`))},n)),this.pendingRequests.set(t.id,{resolve:s,reject:a,timeout:r}),this.target.sendMessage(t)})}async unary(t,n,s,a,r,o){const i=crypto.randomUUID(),l=t.localName,u=t.parent.typeName;if(!u)throw new Error("Service name is required for unary calls");const m=r?vi(t.input,fe(t.input,r)):{};if(n!=null&&n.aborted)throw new Error(`gRPC request aborted before sending: ${u}.${l} (ID: ${i})`);let d;n&&(d=()=>{const g=this.pendingRequests.get(i);g&&(this.pendingRequests.delete(i),clearTimeout(g.timeout),g.reject(new Error(`gRPC request aborted during execution: ${u}.${l} (ID: ${i})`)))},n.addEventListener("abort",d));const h=await this.sendRequest({type:"com.augmentcode.client.rpc.request",id:i,methodLocalName:l,serviceTypeName:u,data:m,timeout:s},s);return n&&d&&n.removeEventListener("abort",d),{stream:!1,method:t,service:t.parent,header:new Headers(a),message:Ei(t.output,h.data),trailer:new Headers}}stream(t,n,s,a,r,o){throw new Error("Streaming is not supported by this transport")}dispose(){this.cleanup();for(const{timeout:t}of this.pendingRequests.values())clearTimeout(t);this.pendingRequests.clear(),this.serviceRegistries.clear()}}c(Or,"PROTOCOL_NAME","com.augmentcode.client.rpc");var Fe;function ma(e){const t=Fe[e];return typeof t!="string"?e.toString():t[0].toLowerCase()+t.substring(1).replace(/[A-Z]/g,n=>"_"+n.toLowerCase())}(function(e){e[e.Canceled=1]="Canceled",e[e.Unknown=2]="Unknown",e[e.InvalidArgument=3]="InvalidArgument",e[e.DeadlineExceeded=4]="DeadlineExceeded",e[e.NotFound=5]="NotFound",e[e.AlreadyExists=6]="AlreadyExists",e[e.PermissionDenied=7]="PermissionDenied",e[e.ResourceExhausted=8]="ResourceExhausted",e[e.FailedPrecondition=9]="FailedPrecondition",e[e.Aborted=10]="Aborted",e[e.OutOfRange=11]="OutOfRange",e[e.Unimplemented=12]="Unimplemented",e[e.Internal=13]="Internal",e[e.Unavailable=14]="Unavailable",e[e.DataLoss=15]="DataLoss",e[e.Unauthenticated=16]="Unauthenticated"})(Fe||(Fe={}));class Ae extends Error{constructor(t,n=Fe.Unknown,s,a,r){super(function(o,i){return o.length?`[${ma(i)}] ${o}`:`[${ma(i)}]`}(t,n)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=t,this.code=n,this.metadata=new Headers(s??{}),this.details=a??[],this.cause=r}static from(t,n=Fe.Unknown){return t instanceof Ae?t:t instanceof Error?t.name=="AbortError"?new Ae(t.message,Fe.Canceled):new Ae(t.message,n,void 0,void 0,t):new Ae(String(t),n,void 0,void 0,t)}static[Symbol.hasInstance](t){return t instanceof Error&&(Object.getPrototypeOf(t)===Ae.prototype||t.name==="ConnectError"&&"code"in t&&typeof t.code=="number"&&"metadata"in t&&"details"in t&&Array.isArray(t.details)&&"rawMessage"in t&&typeof t.rawMessage=="string"&&"cause"in t)}findDetails(t){const n=t.kind==="message"?{getMessage:a=>a===t.typeName?t:void 0}:t,s=[];for(const a of this.details){if("desc"in a){n.getMessage(a.desc.typeName)&&s.push(fe(a.desc,a.value));continue}const r=n.getMessage(a.type);if(r)try{s.push(ms(r,a.value))}catch{}}return s}}var Ii=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),t={},s("next"),s("throw"),s("return"),t[Symbol.asyncIterator]=function(){return this},t);function s(a){t[a]=e[a]&&function(r){return new Promise(function(o,i){(function(l,u,m,d){Promise.resolve(d).then(function(h){l({value:h,done:m})},u)})(o,i,(r=e[a](r)).done,r.value)})}}},_t=function(e){return this instanceof _t?(this.v=e,this):new _t(e)},Ni=function(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,a=n.apply(e,t||[]),r=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",function(d){return function(h){return Promise.resolve(h).then(d,u)}}),s[Symbol.asyncIterator]=function(){return this},s;function o(d,h){a[d]&&(s[d]=function(g){return new Promise(function(f,b){r.push([d,g,f,b])>1||i(d,g)})},h&&(s[d]=h(s[d])))}function i(d,h){try{(g=a[d](h)).value instanceof _t?Promise.resolve(g.value.v).then(l,u):m(r[0][2],g)}catch(f){m(r[0][3],f)}var g}function l(d){i("next",d)}function u(d){i("throw",d)}function m(d,h){d(h),r.shift(),r.length&&i(r[0][0],r[0][1])}},wi=function(e){var t,n;return t={},s("next"),s("throw",function(a){throw a}),s("return"),t[Symbol.iterator]=function(){return this},t;function s(a,r){t[a]=e[a]?function(o){return(n=!n)?{value:_t(e[a](o)),done:!1}:r?r(o):o}:r}},Mr=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),t={},s("next"),s("throw"),s("return"),t[Symbol.asyncIterator]=function(){return this},t);function s(a){t[a]=e[a]&&function(r){return new Promise(function(o,i){(function(l,u,m,d){Promise.resolve(d).then(function(h){l({value:h,done:m})},u)})(o,i,(r=e[a](r)).done,r.value)})}}},at=function(e){return this instanceof at?(this.v=e,this):new at(e)},Ci=function(e){var t,n;return t={},s("next"),s("throw",function(a){throw a}),s("return"),t[Symbol.iterator]=function(){return this},t;function s(a,r){t[a]=e[a]?function(o){return(n=!n)?{value:at(e[a](o)),done:!1}:r?r(o):o}:r}},ki=function(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,a=n.apply(e,t||[]),r=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",function(d){return function(h){return Promise.resolve(h).then(d,u)}}),s[Symbol.asyncIterator]=function(){return this},s;function o(d,h){a[d]&&(s[d]=function(g){return new Promise(function(f,b){r.push([d,g,f,b])>1||i(d,g)})},h&&(s[d]=h(s[d])))}function i(d,h){try{(g=a[d](h)).value instanceof at?Promise.resolve(g.value.v).then(l,u):m(r[0][2],g)}catch(f){m(r[0][3],f)}var g}function l(d){i("next",d)}function u(d){i("throw",d)}function m(d,h){d(h),r.shift(),r.length&&i(r[0][0],r[0][1])}};function Ri(e,t){return function(n,s){const a={};for(const r of n.methods){const o=s(r);o!=null&&(a[r.localName]=o)}return a}(e,n=>{switch(n.methodKind){case"unary":return function(s,a){return async function(r,o){var i,l;const u=await s.unary(a,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,r,o==null?void 0:o.contextValues);return(i=o==null?void 0:o.onHeader)===null||i===void 0||i.call(o,u.header),(l=o==null?void 0:o.onTrailer)===null||l===void 0||l.call(o,u.trailer),u.message}}(t,n);case"server_streaming":return function(s,a){return function(r,o){return ha(s.stream(a,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,function(i){return Ni(this,arguments,function*(){yield _t(yield*wi(Ii(i)))})}([r]),o==null?void 0:o.contextValues),o)}}(t,n);case"client_streaming":return function(s,a){return async function(r,o){var i,l,u,m,d,h;const g=await s.stream(a,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,r,o==null?void 0:o.contextValues);let f;(d=o==null?void 0:o.onHeader)===null||d===void 0||d.call(o,g.header);let b=0;try{for(var y,v=!0,C=Mr(g.message);!(i=(y=await C.next()).done);v=!0)m=y.value,v=!1,f=m,b++}catch(N){l={error:N}}finally{try{v||i||!(u=C.return)||await u.call(C)}finally{if(l)throw l.error}}if(!f)throw new Ae("protocol error: missing response message",Fe.Unimplemented);if(b>1)throw new Ae("protocol error: received extra messages for client streaming method",Fe.Unimplemented);return(h=o==null?void 0:o.onTrailer)===null||h===void 0||h.call(o,g.trailer),f}}(t,n);case"bidi_streaming":return function(s,a){return function(r,o){return ha(s.stream(a,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,r,o==null?void 0:o.contextValues),o)}}(t,n);default:return null}})}function ha(e,t){const n=function(){return ki(this,arguments,function*(){var s,a;const r=yield at(e);(s=t==null?void 0:t.onHeader)===null||s===void 0||s.call(t,r.header),yield at(yield*Ci(Mr(r.message))),(a=t==null?void 0:t.onTrailer)===null||a===void 0||a.call(t,r.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>n.next()})}}function xi(e,t,...n){if(n.length>0)throw new Error;return e.services[t]}async function pa(e){const t=await crypto.subtle.digest("SHA-256",e);return Array.from(new Uint8Array(t)).map(n=>n.toString(16).padStart(2,"0")).join("")}var Dr=(e=>(e.chat="chat",e))(Dr||{}),Fr=(e=>(e.chatMentionFolder="chat-mention-folder",e.chatMentionFile="chat-mention-file",e.chatMentionExternalSource="chat-mention-external-source",e.chatClearContext="chat-clear-context",e.chatRestoreDefaultContext="chat-restore-default-context",e.chatUseActionFind="chat-use-action-find",e.chatUseActionExplain="chat-use-action-explain",e.chatUseActionWriteTest="chat-use-action-write-test",e.chatNewConversation="chat-new-conversation",e.chatEditConversationName="chat-edit-conversation-name",e.chatFailedSmartPasteResolveFile="chat-failed-smart-paste-resolve-file",e.chatPrecomputeSmartPaste="chat-precompute-smart-paste",e.chatSmartPaste="chat-smart-paste",e.chatCodeblockCopy="chat-codeblock-copy",e.chatCodeblockCreate="chat-codeblock-create",e.chatCodeblockGoToFile="chat-codeblock-go-to-file",e.chatCodespanGoToFile="chat-codespan-go-to-file",e.chatCodespanGoToSymbol="chat-codespan-go-to-symbol",e.chatMermaidblockInitialize="chat-mermaidblock-initialize",e.chatMermaidblockToggle="chat-mermaidblock-toggle",e.chatMermaidblockInteract="chat-mermaidblock-interact",e.chatMermaidBlockError="chat-mermaidblock-error",e.chatUseSuggestedQuestion="chat-use-suggested-question",e.chatDisplaySuggestedQuestions="chat-display-suggested-questions",e.setWorkspaceGuidelines="chat-set-workspace-guidelines",e.clearWorkspaceGuidelines="chat-clear-workspace-guidelines",e.setUserGuidelines="chat-set-user-guidelines",e.clearUserGuidelines="chat-clear-user-guidelines",e))(Fr||{});function ga(e){return e.replace(/^data:.*?;base64,/,"")}async function In(e){return new Promise((t,n)=>{const s=new FileReader;s.onload=a=>{var r;return t((r=a.target)==null?void 0:r.result)},s.onerror=n,s.readAsDataURL(e)})}async function Nn(e){return e.length<1e4?Promise.resolve(function(t){const n=atob(t);return Uint8Array.from(n,s=>s.codePointAt(0)||0)}(e)):new Promise((t,n)=>{const s=new Worker(URL.createObjectURL(new Blob([`
            self.onmessage = function(e) {
              try {
                const base64 = e.data;
                const binString = atob(base64);
                const bytes = new Uint8Array(binString.length);
                for (let i = 0; i < binString.length; i++) {
                  bytes[i] = binString.charCodeAt(i);
                }
                self.postMessage(bytes, [bytes.buffer]);
              } catch (error) {
                self.postMessage({ error: error.message });
              }
            };
            `],{type:"application/javascript"})));s.onmessage=function(a){a.data.error?n(new Error(a.data.error)):t(a.data),s.terminate()},s.onerror=function(a){n(a.error),s.terminate()},s.postMessage(e)})}const Ai=xi(hs("Ci5jbGllbnRzL3NpZGVjYXIvbGlicy9wcm90b3MvdGVzdF9zZXJ2aWNlLnByb3RvEgR0ZXN0IhoKC1Rlc3RSZXF1ZXN0EgsKA2ZvbxgBIAEoCSIeCgxUZXN0UmVzcG9uc2USDgoGcmVzdWx0GAEgASgJMngKC1Rlc3RTZXJ2aWNlEjMKClRlc3RNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2USNAoLRXJyb3JNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2ViBnByb3RvMw"),0);var q=(e=>(e.getEditListRequest="agent-get-edit-list-request",e.getEditListResponse="agent-get-edit-list-response",e.getEditChangesByRequestIdRequest="agent-get-edit-changes-by-request-id-request",e.getEditChangesByRequestIdResponse="agent-get-edit-changes-by-request-id-response",e.setCurrentConversation="agent-set-current-conversation",e.migrateConversationId="agent-migrate-conversation-id",e.revertToTimestamp="revert-to-timestamp",e.chatAgentEditAcceptAll="chat-agent-edit-accept-all",e.reportAgentSessionEvent="report-agent-session-event",e.reportAgentRequestEvent="report-agent-request-event",e.chatReviewAgentFile="chat-review-agent-file",e.getAgentEditContentsByRequestId="get-agent-edit-contents-by-request-id",e.getAgentEditContentsByRequestIdResponse="get-agent-edit-contents-by-request-id-response",e.checkHasEverUsedAgent="check-has-ever-used-agent",e.checkHasEverUsedAgentResponse="check-has-ever-used-agent-response",e.setHasEverUsedAgent="set-has-ever-used-agent",e.checkHasEverUsedRemoteAgent="check-has-ever-used-remote-agent",e.checkHasEverUsedRemoteAgentResponse="check-has-ever-used-remote-agent-response",e.setHasEverUsedRemoteAgent="set-has-ever-used-remote-agent",e.getSoundSettings="get-sound-settings",e.getSoundSettingsResponse="get-sound-settings-response",e.updateSoundSettings="update-sound-settings",e.soundSettingsBroadcast="sound-settings-broadcast",e.getSwarmModeSettings="get-swarm-mode-settings",e.getSwarmModeSettingsResponse="get-swarm-mode-settings-response",e.updateSwarmModeSettings="update-swarm-mode-settings",e.swarmModeSettingsBroadcast="swarm-mode-settings-broadcast",e.getChatModeRequest="get-chat-mode-request",e.getChatModeResponse="get-chat-mode-response",e))(q||{}),qt=(e=>(e.checkToolCallSafeRequest="check-tool-call-safe-request",e.checkToolCallSafeResponse="check-tool-call-safe-response",e.closeAllToolProcesses="close-all-tool-processes",e.getToolIdentifierRequest="get-tool-identifier-request",e.getToolIdentifierResponse="get-tool-identifier-response",e))(qt||{}),Ht=(e=>(e.loadConversationExchangesRequest="load-conversation-exchanges-request",e.loadConversationExchangesResponse="load-conversation-exchanges-response",e.loadExchangesByUuidsRequest="load-exchanges-by-uuids-request",e.loadExchangesByUuidsResponse="load-exchanges-by-uuids-response",e.saveExchangesRequest="save-exchanges-request",e.saveExchangesResponse="save-exchanges-response",e.deleteExchangesRequest="delete-exchanges-request",e.deleteExchangesResponse="delete-exchanges-response",e.deleteConversationExchangesRequest="delete-conversation-exchanges-request",e.deleteConversationExchangesResponse="delete-conversation-exchanges-response",e.countExchangesRequest="count-exchanges-request",e.countExchangesResponse="count-exchanges-response",e))(Ht||{});class Oi{constructor(t=[]){c(this,"_items",[]);c(this,"_focusedItemIdx");c(this,"_subscribers",new Set);c(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));c(this,"setItems",t=>{this._items=t,this._items.length===0?this.setFocusIdx(void 0):this._focusedItemIdx!==void 0&&this._focusedItemIdx>=this._items.length?this.setFocusIdx(this._items.length-1):this._focusedItemIdx===void 0?this.setFocusIdx(void 0):this.setFocusIdx(this._focusedItemIdx)});c(this,"setFocus",t=>{if(t!==void 0&&t===this.focusedItem)return;const n=t?this._items.indexOf(t):-1;n===-1?this.setFocusIdx(void 0):this.setFocusIdx(n)});c(this,"setFocusIdx",t=>{if(t===this._focusedItemIdx||this._items.length===0)return;if(t===void 0)return this._focusedItemIdx=void 0,void this.notifySubscribers();const n=Math.floor(t/this._items.length)*this._items.length;this._focusedItemIdx=(t-n)%this._items.length,this.notifySubscribers()});c(this,"initFocusIdx",t=>this._focusedItemIdx===void 0&&(this.setFocusIdx(t),!0));c(this,"focusNext",()=>{const t=this.nextIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});c(this,"focusPrev",()=>{const t=this.prevIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});c(this,"prevIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?this._items.length-1:t.nowrap&&this._focusedItemIdx===0?0:(this._focusedItemIdx-1+this._items.length)%this._items.length});c(this,"nextIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?0:t.nowrap&&this._focusedItemIdx===this._items.length-1?this._items.length-1:(this._focusedItemIdx+1)%this._items.length});c(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});this._items=t}get items(){return this._items}get focusedItem(){if(this._focusedItemIdx!==void 0)return this._items[this._focusedItemIdx]}get focusedItemIdx(){return this._focusedItemIdx}}var Mi=(e=>(e[e.unspecified=0]="unspecified",e[e.userGuidelines=1]="userGuidelines",e[e.augmentGuidelines=2]="augmentGuidelines",e[e.rules=3]="rules",e))(Mi||{}),Di=(e=>(e[e.unspecified=0]="unspecified",e[e.manuallyCreated=1]="manuallyCreated",e[e.auto=2]="auto",e[e.selectedDirectory=3]="selectedDirectory",e[e.selectedFile=4]="selectedFile",e))(Di||{});function Fi(e){return e===void 0?{num_lines:-1,num_chars:-1}:{num_lines:e.split(`
`).length,num_chars:e.length}}class Ur{constructor(){this.tracingData={flags:{},nums:{},string_stats:{},request_ids:{}}}setFlag(t,n=!0){this.tracingData.flags[t]={value:n,timestamp:new Date().toISOString()}}getFlag(t){const n=this.tracingData.flags[t];return n==null?void 0:n.value}setNum(t,n){this.tracingData.nums[t]={value:n,timestamp:new Date().toISOString()}}getNum(t){const n=this.tracingData.nums[t];return n==null?void 0:n.value}setStringStats(t,n){this.tracingData.string_stats[t]={value:Fi(n),timestamp:new Date().toISOString()}}setRequestId(t,n){this.tracingData.request_ids[t]={value:n,timestamp:new Date().toISOString()}}}var Ui=(e=>(e[e.unspecified=0]="unspecified",e[e.classify_and_distill=1]="classify_and_distill",e[e.orientation=2]="orientation",e))(Ui||{}),Pi=(e=>(e.start="start",e.end="end",e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noMemoryData="noMemoryData",e.agenticTurnHasRememberToolCall="agenticTurnHasRememberToolCall",e.emptyMemory="emptyMemory",e.removeUserExchangeMemoryFailed="removeUserExchangeMemoryFailed",e))(Pi||{});class Pr extends Ur{constructor(){super()}static create(){return new Pr}}var Li=(e=>(e.openedAgentConversation="opened-agent-conversation",e.revertCheckpoint="revert-checkpoint",e.agentInterruption="agent-interruption",e.sentUserMessage="sent-user-message",e.rememberToolCall="remember-tool-call",e.openedMemoriesFile="opened-memories-file",e.initialOrientation="initial-orientation",e.classifyAndDistill="classify-and-distill",e.flushMemories="flush-memories",e.vsCodeTerminalCwdNotAbsolute="vs-code-terminal-cwd-not-absolute",e.vsCodeTerminalCwdDoesNotExist="vs-code-terminal-cwd-does-not-exist",e.vsCodeTerminalShellIntegrationNotAvailable="vs-code-terminal-shell-integration-not-available",e.vsCodeTerminalReadingApproximateOutput="vs-code-terminal-reading-approximate-output",e.vsCodeTerminalTimedOutWaitingForNoopCommand="vs-code-terminal-timed-out-waiting-for-noop-command",e.vsCodeTerminalTimedOutWaitingForSetCwdCommand="vs-code-terminal-timed-out-waiting-for-set-cwd-command",e.vsCodeTerminalFailedToUseShellIntegration="vs-code-terminal-failed-to-use-shell-integration",e.vsCodeTerminalLastCommandIsSameAsCurrent="vs-code-terminal-last-command-is-same-as-current",e.vsCodeTerminalPollingDeterminedProcessIsDone="vs-code-terminal-polling-determined-process-is-done",e.vsCodeScriptStrategyPollingDeterminedProcessIsDone="vs-code-script-strategy-polling-determined-process-is-done",e.vsCodeTerminalFailedToReadOutput="vs-code-terminal-failed-to-read-output",e.vsCodeTerminalBuggyOutput="vs-code-terminal-buggy-output",e.vsCodeTerminalBuggyExecutionEvents="vs-code-terminal-buggy-execution-events",e.vsCodeTerminalUnsupportedVSCodeShell="vs-code-terminal-unsupported-vscode-shell",e.vsCodeTerminalFailedToFindGitBash="vs-code-terminal-failed-to-find-git-bash",e.vsCodeTerminalFailedToFindPowerShell="vs-code-terminal-failed-to-find-powershell",e.vsCodeTerminalNoSupportedShellsFound="vs-code-terminal-no-supported-shells-found",e.vsCodeTerminalSettingsChanged="vs-code-terminal-settings-changed",e.vsCodeTerminalWaitTimeout="vs-code-terminal-wait-timeout",e.vsCodeTerminalErrorLoadingSettings="vs-code-terminal-error-loading-settings",e.vsCodeTerminalErrorCheckingForShellUpdates="vs-code-terminal-error-checking-for-shell-updates",e.vsCodeTerminalErrorCleaningUpTempDir="vs-code-terminal-error-cleaning-up-temp-dir",e.vsCodeTerminalErrorInitializingShells="vs-code-terminal-error-initializing-shells",e.vsCodeTerminalErrorCheckingShellCapability="vs-code-terminal-error-checking-shell-capability",e.vsCodeTerminalErrorCreatingZshEnvironment="vs-code-terminal-error-creating-zsh-environment",e.vsCodeTerminalMissedStartEvent="vs-code-terminal-missed-start-event",e.vsCodeTerminalReadStreamTimeoutWhenProcessIsComplete="vs-code-terminal-read-stream-timeout-when-process-is-complete",e.vsCodeTerminalScriptCommandNotAvailable="vs-code-terminal-script-command-not-available",e.enhancedPrompt="enhanced-prompt",e.memoriesMove="memories-move",e.rulesImported="rules-imported",e.taskListUsage="task-list-usage",e.memoryUsage="memory-usage",e.contentTruncation="content-truncation",e))(Li||{}),zt=(e=>(e.sentUserMessage="sent-user-message",e.chatHistorySummarization="chat-history-summarization",e.enhancedPrompt="enhanced-prompt",e.firstTokenReceived="first-token-received",e.chatHistoryTruncated="chat-history-truncated",e))(zt||{}),$i=(e=>(e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.start="start",e.end="end",e.noPendingUserMessage="noPendingUserMessage",e.startSendSilentExchange="startSendSilentExchange",e.sendSilentExchangeRequestId="sendSilentExchangeRequestId",e.sendSilentExchangeResponseStats="sendSilentExchangeResponseStats",e.noRequestId="noRequestId",e.conversationChanged="conversationChanged",e.explanationStats="explanationStats",e.contentStats="contentStats",e.invalidResponse="invalidResponse",e.worthRemembering="worthRemembering",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noLastUserExchangeRequestId="noLastUserExchangeRequestId",e))($i||{});class Lr extends Ur{constructor(){super()}static create(){return new Lr}}var qi=(e=>(e.remoteAgentSetup="remote-agent-setup",e.setupScript="setup-script",e.sshInteraction="ssh-interaction",e.notificationBell="notification-bell",e.diffPanel="diff-panel",e.setupPageOpened="setup-page-opened",e.githubAPIFailure="github-api-failure",e.remoteAgentCreated="remote-agent-created",e.changesApplied="changes-applied",e.createdPR="created-pr",e.modeSelector="mode-selector",e.remoteAgentSetupWindow="remote-agent-setup-window",e.remoteAgentThreadList="remote-agent-thread-list",e.remoteAgentNewThreadButton="remote-agent-new-thread-button",e))(qi||{}),Hi=(e=>(e[e.unknownSourceControl=0]="unknownSourceControl",e[e.git=1]="git",e[e.github=2]="github",e))(Hi||{}),Gi=(e=>(e[e.unknownMode=0]="unknownMode",e[e.chat=1]="chat",e[e.agent=2]="agent",e[e.remoteAgent=3]="remoteAgent",e))(Gi||{}),Vi=(e=>(e[e.unknownModeSelectorAction=0]="unknownModeSelectorAction",e[e.open=1]="open",e[e.close=2]="close",e[e.select=3]="select",e[e.init=4]="init",e))(Vi||{}),Bi=(e=>(e[e.unknownSetupWindowAction=0]="unknownSetupWindowAction",e[e.open=1]="open",e[e.close=2]="close",e[e.selectRepo=3]="selectRepo",e[e.selectBranch=4]="selectBranch",e[e.selectSetupScript=5]="selectSetupScript",e[e.autoGenerateSetupScript=6]="autoGenerateSetupScript",e[e.manuallyCreateSetupScript=7]="manuallyCreateSetupScript",e[e.typeInPromptWindow=8]="typeInPromptWindow",e[e.clickRewritePrompt=9]="clickRewritePrompt",e[e.enableNotifications=10]="enableNotifications",e[e.disableNotifications=11]="disableNotifications",e[e.clickCreateAgent=12]="clickCreateAgent",e))(Bi||{}),Yi=(e=>(e[e.unknownAgentListAction=0]="unknownAgentListAction",e[e.open=1]="open",e[e.close=2]="close",e[e.selectAgent=3]="selectAgent",e[e.deleteAgent=4]="deleteAgent",e[e.pinAgent=5]="pinAgent",e[e.unpinAgent=6]="unpinAgent",e))(Yi||{}),ji=(e=>(e[e.unknown=0]="unknown",e[e.click=1]="click",e[e.open=2]="open",e[e.close=3]="close",e))(ji||{}),Ki=(e=>(e[e.unknown=0]="unknown",e[e.chat=1]="chat",e[e.agent=2]="agent",e[e.remoteAgent=3]="remoteAgent",e))(Ki||{}),Wi=(e=>(e[e.unknown=0]="unknown",e[e.addTask=1]="addTask",e[e.addSubtask=2]="addSubtask",e[e.updateTaskStatus=3]="updateTaskStatus",e[e.updateTaskName=4]="updateTaskName",e[e.updateTaskDescription=5]="updateTaskDescription",e[e.reorganizeTaskList=6]="reorganizeTaskList",e[e.deleteTask=7]="deleteTask",e[e.runSingleTask=8]="runSingleTask",e[e.runAllTasks=9]="runAllTasks",e[e.viewTaskList=10]="viewTaskList",e[e.exportTaskList=11]="exportTaskList",e[e.importTaskList=12]="importTaskList",e[e.syncTaskList=13]="syncTaskList",e))(Wi||{}),zi=(e=>(e[e.unknown=0]="unknown",e[e.user=1]="user",e[e.agent=2]="agent",e))(zi||{}),Xi=(e=>(e[e.unknown=0]="unknown",e[e.saveMemory=1]="saveMemory",e[e.discardMemory=2]="discardMemory",e[e.editMemory=3]="editMemory",e[e.viewMemories=4]="viewMemories",e[e.refreshMemories=5]="refreshMemories",e[e.filterByState=6]="filterByState",e[e.filterByVersion=7]="filterByVersion",e[e.openMemoriesFile=8]="openMemoriesFile",e[e.createMemory=9]="createMemory",e))(Xi||{}),Ji=(e=>(e[e.unknown=0]="unknown",e[e.user=1]="user",e[e.agent=2]="agent",e))(Ji||{});function Zi(e,t,n=1e3){let s=null,a=0;const r=Xn(t),o=()=>{const i=(()=>{const l=Date.now();if(s!==null&&l-a<n)return s;const u=e();return s=u,a=l,u})();r.set(i)};return{subscribe:r.subscribe,resetCache:()=>{s=null,o()},updateStore:o}}var $r=(e=>(e[e.unset=0]="unset",e[e.positive=1]="positive",e[e.negative=2]="negative",e))($r||{}),Qi=(e=>(e.longRunning="longRunning",e.running="running",e.done="done",e))(Qi||{}),el=(e=>(e.initializing="initializing",e.enabled="enabled",e.disabled="disabled",e.partial="partial",e))(el||{});const bs=class bs{static hasFrontmatter(t){return this.frontmatterRegex.test(t)}static extractFrontmatter(t){const n=t.match(this.frontmatterRegex);return n&&n[1]?n[1]:null}static extractContent(t){return t.replace(this.frontmatterRegex,"")}static parseBoolean(t,n,s=!0){const a=this.extractFrontmatter(t);if(a){const r=new RegExp(`${n}\\s*:\\s*(true|false)`,"i"),o=a.match(r);if(o&&o[1])return o[1].toLowerCase()==="true"}return s}static parseString(t,n,s=""){const a=this.extractFrontmatter(t);if(a){const r=new RegExp(`${n}\\s*:\\s*["']?([^"'
]*)["']?`,"i"),o=a.match(r);if(o&&o[1])return o[1].trim()}return s}static updateFrontmatter(t,n,s){const a=t.match(this.frontmatterRegex),r=typeof s!="string"||/^(true|false)$/.test(s.toLowerCase())?String(s):`"${s}"`;if(a){const o=a[1],i=new RegExp(`(${n}\\s*:\\s*)([^\\n]*)`,"i");if(o.match(i)){const l=o.replace(i,`$1${r}`);return t.replace(this.frontmatterRegex,`---
${l}---
`)}{const l=`${o.endsWith(`
`)?o:o+`
`}${n}: ${r}
`;return t.replace(this.frontmatterRegex,`---
${l}---
`)}}return`---
${n}: ${r}
---

${t}`}static createFrontmatter(t,n){let s=t;this.hasFrontmatter(s)&&(s=this.extractContent(s));for(const[a,r]of Object.entries(n))s=this.updateFrontmatter(s,a,r);return s}};bs.frontmatterRegex=/^---\s*\n([\s\S]*?)\n---\s*\n/;let ae=bs;const qe=class qe{static parseRuleFile(t,n){const s=ae.parseString(t,this.DESCRIPTION_FRONTMATTER_KEY,""),a=ae.extractContent(t);return{type:this.getRuleTypeFromContent(t),path:n,content:a,description:s||void 0}}static formatRuleFileForMarkdown(t){let n=t.content;return n=ae.updateFrontmatter(n,this.TYPE_FRONTMATTER_KEY,this.mapRuleTypeToString(t.type)),t.description&&(n=ae.updateFrontmatter(n,this.DESCRIPTION_FRONTMATTER_KEY,t.description)),n}static getAlwaysApplyFrontmatterKey(t){return ae.parseBoolean(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,!1)}static extractContent(t){return ae.extractContent(t)}static updateAlwaysApplyFrontmatterKey(t,n){return ae.updateFrontmatter(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,n)}static getDescriptionFrontmatterKey(t){return ae.parseString(t,this.DESCRIPTION_FRONTMATTER_KEY,"")}static updateDescriptionFrontmatterKey(t,n){return ae.updateFrontmatter(t,this.DESCRIPTION_FRONTMATTER_KEY,n)}static mapStringToRuleType(t){switch(t.toLowerCase()){case"always_apply":return ye.ALWAYS_ATTACHED;case"manual":return ye.MANUAL;case"agent_requested":return ye.AGENT_REQUESTED;default:return this.DEFAULT_RULE_TYPE}}static mapRuleTypeToString(t){switch(t){case ye.ALWAYS_ATTACHED:return"always_apply";case ye.MANUAL:return"manual";case ye.AGENT_REQUESTED:return"agent_requested";default:return"manual"}}static isValidTypeValue(t){return this.VALID_TYPE_VALUES.includes(t.toLowerCase())}static getTypeFrontmatterKey(t){return ae.parseString(t,this.TYPE_FRONTMATTER_KEY,"")}static updateTypeFrontmatterKey(t,n){const s=this.mapRuleTypeToString(n);return ae.updateFrontmatter(t,this.TYPE_FRONTMATTER_KEY,s)}static getRuleTypeFromContent(t){const n=this.getTypeFrontmatterKey(t);if(n&&this.isValidTypeValue(n))return this.mapStringToRuleType(n);const s=this.getAlwaysApplyFrontmatterKey(t),a=this.getDescriptionFrontmatterKey(t);return s?ye.ALWAYS_ATTACHED:a&&a.trim()!==""?ye.AGENT_REQUESTED:ye.MANUAL}};qe.ALWAYS_APPLY_FRONTMATTER_KEY="alwaysApply",qe.DESCRIPTION_FRONTMATTER_KEY="description",qe.TYPE_FRONTMATTER_KEY="type",qe.VALID_TYPE_VALUES=["always_apply","manual","agent_requested"],qe.DEFAULT_RULE_TYPE=ye.MANUAL;let fa=qe;const tl=".augment",nl="rules",Qu=".augment-guidelines";function Z(e,t){return t in e&&e[t]!==void 0}function sl(e){return Z(e,"file")}function al(e){return Z(e,"recentFile")}function rl(e){return Z(e,"folder")}function ol(e){return Z(e,"sourceFolder")}function ec(e){return Z(e,"sourceFolderGroup")}function tc(e){return Z(e,"selection")}function il(e){return Z(e,"externalSource")}function nc(e){return Z(e,"allDefaultContext")}function sc(e){return Z(e,"clearContext")}function ac(e){return Z(e,"userGuidelines")}function rc(e){return Z(e,"agentMemories")}function qr(e){return Z(e,"personality")}function ll(e){return Z(e,"rule")}function ul(e){return Z(e,"task")}const oc={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},ic={clearContext:!0,label:"Clear Context",id:"clearContext"},lc={userGuidelines:{overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},uc={agentMemories:{},label:"Agent Memories",id:"agentMemories"},ya=[{personality:{type:B.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:B.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:B.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:B.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function cc(e){return Z(e,"group")}function dc(e){const t=new Map;return e.forEach(n=>{sl(n)?t.set("file",[...t.get("file")??[],n]):al(n)?t.set("recentFile",[...t.get("recentFile")??[],n]):rl(n)?t.set("folder",[...t.get("folder")??[],n]):il(n)?t.set("externalSource",[...t.get("externalSource")??[],n]):ol(n)?t.set("sourceFolder",[...t.get("sourceFolder")??[],n]):qr(n)?t.set("personality",[...t.get("personality")??[],n]):ll(n)&&t.set("rule",[...t.get("rule")??[],n])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:t.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:t.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:t.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:t.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:t.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:t.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:t.get("rule")??[]}}].filter(n=>n.group.items.length>0)}function cl(e){const t=(n={rootPath:e.repoRoot,relPath:e.pathName}).rootPath+"/"+n.relPath;var n;const s={label:co(e.pathName).split("/").filter(a=>a.trim()!=="").pop()||"",name:t,id:t};if(e.fullRange){const a=`:L${e.fullRange.startLineNumber}-${e.fullRange.endLineNumber}`;s.label+=a,s.name+=a,s.id+=a}else if(e.range){const a=`:L${e.range.start}-${e.range.stop}`;s.label+=a,s.name+=a,s.id+=a}return s}function dl(e){const t=e.path.split("/"),n=t[t.length-1],s=n.endsWith(".md")?n.slice(0,-3):n,a=`${tl}/${nl}/${e.path}`;return{label:s,name:a,id:a}}var x=(e=>(e[e.unknown=0]="unknown",e[e.new=1]="new",e[e.checkingSafety=2]="checkingSafety",e[e.runnable=3]="runnable",e[e.running=4]="running",e[e.completed=5]="completed",e[e.error=6]="error",e[e.cancelling=7]="cancelling",e[e.cancelled=8]="cancelled",e))(x||{});function wn(e){return`${e.requestId};${e.toolUseId}`}function ba(e){const[t,n]=e.split(";");return{requestId:t,toolUseId:n}}var ml=(e=>(e.readFile="read-file",e.saveFile="save-file",e.editFile="edit-file",e.clarify="clarify",e.onboardingSubAgent="onboarding-sub-agent",e.launchProcess="launch-process",e.killProcess="kill-process",e.readProcess="read-process",e.writeProcess="write-process",e.listProcesses="list-processes",e.waitProcess="wait-process",e.openBrowser="open-browser",e.strReplaceEditor="str-replace-editor",e.remember="remember",e.diagnostics="diagnostics",e.setupScript="setup-script",e.readTerminal="read-terminal",e.gitCommitRetrieval="git-commit-retrieval",e.memoryRetrieval="memory-retrieval",e.startWorkerAgent="start_worker_agent",e.readWorkerState="read_worker_state",e.waitForWorkerAgent="wait_for_worker_agent",e.sendInstructionToWorkerAgent="send_instruction_to_worker_agent",e.stopWorkerAgent="stop_worker_agent",e.deleteWorkerAgent="delete_worker_agent",e.readWorkerAgentEdits="read_worker_agent_edits",e.applyWorkerAgentEdits="apply_worker_agent_edits",e.LocalSubAgent="local-sub-agent",e))(ml||{}),hl=(e=>(e.remoteToolHost="remoteToolHost",e.localToolHost="localToolHost",e.sidecarToolHost="sidecarToolHost",e.mcpHost="mcpHost",e))(hl||{}),Xt=(e=>(e[e.ContentText=0]="ContentText",e[e.ContentImage=1]="ContentImage",e))(Xt||{}),pl=(e=>(e[e.Unsafe=0]="Unsafe",e[e.Safe=1]="Safe",e[e.Check=2]="Check",e))(pl||{}),gl=(e=>(e[e.Unknown=0]="Unknown",e[e.WebSearch=1]="WebSearch",e[e.GitHubApi=8]="GitHubApi",e[e.Linear=12]="Linear",e[e.Jira=13]="Jira",e[e.Confluence=14]="Confluence",e[e.Notion=15]="Notion",e[e.Supabase=16]="Supabase",e[e.Glean=17]="Glean",e))(gl||{});function _a(e,t){return function(n,s){if(n.length<=s||n.length===0)return{truncatedText:n};const a=n.split(`
`),r="... additional lines truncated ..."+(a[0].endsWith("\r")?"\r":"");let o,i="";if(a.length<2||a[0].length+a[a.length-1].length+r.length>s){const l=Math.floor(s/2);i=[n.slice(0,l),"<...>",n.slice(-l)].join(""),o=[1,1,a.length,a.length]}else{const l=[],u=[];let m=r.length+1;for(let d=0;d<Math.floor(a.length/2);d++){const h=a[d],g=a[a.length-1-d],f=h.length+g.length+2;if(m+f>s)break;m+=f,l.push(h),u.push(g)}o=[1,l.length,a.length-u.length+1,a.length],l.push(r),l.push(...u.reverse()),i=l.join(`
`)}return{truncatedText:i,shownRangeWhenTruncated:o}}(e,t).truncatedText}function fl(e){var n;if(!e)return Nt.IMAGE_FORMAT_UNSPECIFIED;switch((n=e.split("/")[1])==null?void 0:n.toLowerCase()){case"jpeg":case"jpg":return Nt.JPEG;case"png":return Nt.PNG;default:return Nt.IMAGE_FORMAT_UNSPECIFIED}}function yl(e,t,n){var a,r;if(e.phase!==x.cancelled&&e.phase!==x.completed&&e.phase!==x.error)return;let s;return(a=e.result)!=null&&a.contentNodes?(s=function(o,i){return o.map(l=>l.type===Xt.ContentText?{type:pn.CONTENT_TEXT,text_content:l.text_content}:l.type===Xt.ContentImage&&l.image_content&&i?{type:pn.CONTENT_IMAGE,image_content:{image_data:l.image_content.image_data,format:fl(l.image_content.media_type)}}:{type:pn.CONTENT_TEXT,text_content:"[Error: Invalid content node]"})}(e.result.contentNodes,n),{content:"",is_error:e.result.isError,request_id:e.result.requestId,tool_use_id:t,content_nodes:s}):((r=e.result)==null?void 0:r.text)!==void 0?{content:e.result.text,is_error:e.result.isError,request_id:e.result.requestId,tool_use_id:t}:void 0}function bl(e=[]){let t;for(const n of e){if(n.type===D.TOOL_USE)return n;n.type===D.TOOL_USE_START&&(t=n)}return t}function _l(e,t,n,s){if(!e||!t)return[];let a=!1;return t.filter(r=>{var i;const o=s!=null&&s.isActive&&r.tool_use?s.getToolUseState(r.tool_use.tool_use_id):n.getToolUseState(r.requestId??e,(i=r.tool_use)==null?void 0:i.tool_use_id);return a===!1&&o.phase!==x.new&&o.phase!==x.unknown&&o.phase!==x.checkingSafety&&r.tool_use!==void 0||(o.phase===x.runnable&&(a=!0),!1)})}function mc(e,t){if(e.contentNodes&&e.contentNodes.length>0){const n=e.contentNodes.map(s=>{if(s.type===Xt.ContentText){let a="";return s.text_content&&(a=_a(s.text_content,t/e.contentNodes.length)),{...s,text_content:a}}return s});return{...e,contentNodes:n}}return{...e,text:_a(e.text,t)}}const vl="__NEW_AGENT__",hc=e=>e.chatItemType===void 0,pc=(e,t)=>{var r;const n=e.chatHistory.at(-1);if(!n||!L(n))return $e.notRunning;if(!(n.status===E.success||n.status===E.failed||n.status===E.cancelled))return $e.running;const s=((r=n.structured_output_nodes)==null?void 0:r.filter(o=>o.type===D.TOOL_USE&&!!o.tool_use))??[];let a;if(a=t.enableParallelTools?_l(n.request_id,s,e).at(-1):s.at(-1),!a||!a.tool_use)return $e.notRunning;switch(e.getToolUseState(n.request_id,a.tool_use.tool_use_id).phase){case x.runnable:return $e.awaitingUserAction;case x.cancelled:return $e.notRunning;default:return $e.running}},Yn=e=>L(e)&&!!e.request_message,Tl=e=>e.chatHistory.findLast(t=>Yn(t)),gc=(e,t)=>{const n=Tl(e);return n!=null&&n.request_id?e.historyFrom(n.request_id,!0).filter(s=>L(s)&&(!t||t(s))):[]},fc=e=>{var s;const t=e.chatHistory.at(-1);if(!(t!=null&&t.request_id)||!L(t))return!1;const n=((s=t.structured_output_nodes)==null?void 0:s.filter(a=>a.type===D.TOOL_USE))??[];for(const a of n)if(a.tool_use&&e.getToolUseState(t.request_id,a.tool_use.tool_use_id).phase===x.runnable)return e.updateToolUseState({requestId:t.request_id,toolUseId:a.tool_use.tool_use_id,phase:x.cancelled}),!0;return!1};function El(e,t){const n=e.customPersonalityPrompts;if(n)switch(t){case B.DEFAULT:if(n.agent&&n.agent.trim()!=="")return n.agent;break;case B.PROTOTYPER:if(n.prototyper&&n.prototyper.trim()!=="")return n.prototyper;break;case B.BRAINSTORM:if(n.brainstorm&&n.brainstorm.trim()!=="")return n.brainstorm;break;case B.REVIEWER:if(n.reviewer&&n.reviewer.trim()!=="")return n.reviewer}return Sl[t]}const Sl={[B.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[B.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[B.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[B.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};var oe=(e=>(e.NOT_STARTED="NOT_STARTED",e.IN_PROGRESS="IN_PROGRESS",e.CANCELLED="CANCELLED",e.COMPLETE="COMPLETE",e))(oe||{}),ys=(e=>(e.USER="USER",e.AGENT="AGENT",e))(ys||{}),Hr={},Jt={},Zt={};let Rt;Object.defineProperty(Zt,"__esModule",{value:!0}),Zt.default=function(){if(!Rt&&(Rt=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Rt))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Rt(Il)};const Il=new Uint8Array(16);var Ue={},Be={},Qt={};Object.defineProperty(Qt,"__esModule",{value:!0}),Qt.default=void 0;Qt.default=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,Object.defineProperty(Be,"__esModule",{value:!0}),Be.default=void 0;var xt,Nl=(xt=Qt)&&xt.__esModule?xt:{default:xt},wl=function(e){return typeof e=="string"&&Nl.default.test(e)};Be.default=wl,Object.defineProperty(Ue,"__esModule",{value:!0}),Ue.default=void 0,Ue.unsafeStringify=Gr;var Cl=function(e){return e&&e.__esModule?e:{default:e}}(Be);const H=[];for(let e=0;e<256;++e)H.push((e+256).toString(16).slice(1));function Gr(e,t=0){return H[e[t+0]]+H[e[t+1]]+H[e[t+2]]+H[e[t+3]]+"-"+H[e[t+4]]+H[e[t+5]]+"-"+H[e[t+6]]+H[e[t+7]]+"-"+H[e[t+8]]+H[e[t+9]]+"-"+H[e[t+10]]+H[e[t+11]]+H[e[t+12]]+H[e[t+13]]+H[e[t+14]]+H[e[t+15]]}var kl=function(e,t=0){const n=Gr(e,t);if(!(0,Cl.default)(n))throw TypeError("Stringified UUID is invalid");return n};Ue.default=kl,Object.defineProperty(Jt,"__esModule",{value:!0}),Jt.default=void 0;var Rl=function(e){return e&&e.__esModule?e:{default:e}}(Zt),xl=Ue;let va,Cn,kn=0,Rn=0;var Al=function(e,t,n){let s=t&&n||0;const a=t||new Array(16);let r=(e=e||{}).node||va,o=e.clockseq!==void 0?e.clockseq:Cn;if(r==null||o==null){const h=e.random||(e.rng||Rl.default)();r==null&&(r=va=[1|h[0],h[1],h[2],h[3],h[4],h[5]]),o==null&&(o=Cn=16383&(h[6]<<8|h[7]))}let i=e.msecs!==void 0?e.msecs:Date.now(),l=e.nsecs!==void 0?e.nsecs:Rn+1;const u=i-kn+(l-Rn)/1e4;if(u<0&&e.clockseq===void 0&&(o=o+1&16383),(u<0||i>kn)&&e.nsecs===void 0&&(l=0),l>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");kn=i,Rn=l,Cn=o,i+=122192928e5;const m=(1e4*(268435455&i)+l)%4294967296;a[s++]=m>>>24&255,a[s++]=m>>>16&255,a[s++]=m>>>8&255,a[s++]=255&m;const d=i/4294967296*1e4&268435455;a[s++]=d>>>8&255,a[s++]=255&d,a[s++]=d>>>24&15|16,a[s++]=d>>>16&255,a[s++]=o>>>8|128,a[s++]=255&o;for(let h=0;h<6;++h)a[s+h]=r[h];return t||(0,xl.unsafeStringify)(a)};Jt.default=Al;var en={},Oe={},vt={};Object.defineProperty(vt,"__esModule",{value:!0}),vt.default=void 0;var Ol=function(e){return e&&e.__esModule?e:{default:e}}(Be),Ml=function(e){if(!(0,Ol.default)(e))throw TypeError("Invalid UUID");let t;const n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n};vt.default=Ml,Object.defineProperty(Oe,"__esModule",{value:!0}),Oe.URL=Oe.DNS=void 0,Oe.default=function(e,t,n){function s(a,r,o,i){var l;if(typeof a=="string"&&(a=function(m){m=unescape(encodeURIComponent(m));const d=[];for(let h=0;h<m.length;++h)d.push(m.charCodeAt(h));return d}(a)),typeof r=="string"&&(r=(0,Fl.default)(r)),((l=r)===null||l===void 0?void 0:l.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let u=new Uint8Array(16+a.length);if(u.set(r),u.set(a,r.length),u=n(u),u[6]=15&u[6]|t,u[8]=63&u[8]|128,o){i=i||0;for(let m=0;m<16;++m)o[i+m]=u[m];return o}return(0,Dl.unsafeStringify)(u)}try{s.name=e}catch{}return s.DNS=Vr,s.URL=Br,s};var Dl=Ue,Fl=function(e){return e&&e.__esModule?e:{default:e}}(vt);const Vr="6ba7b810-9dad-11d1-80b4-00c04fd430c8";Oe.DNS=Vr;const Br="6ba7b811-9dad-11d1-80b4-00c04fd430c8";Oe.URL=Br;var tn={};function Ta(e){return 14+(e+64>>>9<<4)+1}function Me(e,t){const n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function dn(e,t,n,s,a,r){return Me((o=Me(Me(t,e),Me(s,r)))<<(i=a)|o>>>32-i,n);var o,i}function Y(e,t,n,s,a,r,o){return dn(t&n|~t&s,e,t,a,r,o)}function j(e,t,n,s,a,r,o){return dn(t&s|n&~s,e,t,a,r,o)}function K(e,t,n,s,a,r,o){return dn(t^n^s,e,t,a,r,o)}function W(e,t,n,s,a,r,o){return dn(n^(t|~s),e,t,a,r,o)}Object.defineProperty(tn,"__esModule",{value:!0}),tn.default=void 0;var Ul=function(e){if(typeof e=="string"){const t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(let n=0;n<t.length;++n)e[n]=t.charCodeAt(n)}return function(t){const n=[],s=32*t.length,a="0123456789abcdef";for(let r=0;r<s;r+=8){const o=t[r>>5]>>>r%32&255,i=parseInt(a.charAt(o>>>4&15)+a.charAt(15&o),16);n.push(i)}return n}(function(t,n){t[n>>5]|=128<<n%32,t[Ta(n)-1]=n;let s=1732584193,a=-271733879,r=-1732584194,o=271733878;for(let i=0;i<t.length;i+=16){const l=s,u=a,m=r,d=o;s=Y(s,a,r,o,t[i],7,-680876936),o=Y(o,s,a,r,t[i+1],12,-389564586),r=Y(r,o,s,a,t[i+2],17,606105819),a=Y(a,r,o,s,t[i+3],22,-1044525330),s=Y(s,a,r,o,t[i+4],7,-176418897),o=Y(o,s,a,r,t[i+5],12,1200080426),r=Y(r,o,s,a,t[i+6],17,-1473231341),a=Y(a,r,o,s,t[i+7],22,-45705983),s=Y(s,a,r,o,t[i+8],7,1770035416),o=Y(o,s,a,r,t[i+9],12,-1958414417),r=Y(r,o,s,a,t[i+10],17,-42063),a=Y(a,r,o,s,t[i+11],22,-1990404162),s=Y(s,a,r,o,t[i+12],7,1804603682),o=Y(o,s,a,r,t[i+13],12,-40341101),r=Y(r,o,s,a,t[i+14],17,-1502002290),a=Y(a,r,o,s,t[i+15],22,1236535329),s=j(s,a,r,o,t[i+1],5,-165796510),o=j(o,s,a,r,t[i+6],9,-1069501632),r=j(r,o,s,a,t[i+11],14,643717713),a=j(a,r,o,s,t[i],20,-373897302),s=j(s,a,r,o,t[i+5],5,-701558691),o=j(o,s,a,r,t[i+10],9,38016083),r=j(r,o,s,a,t[i+15],14,-660478335),a=j(a,r,o,s,t[i+4],20,-405537848),s=j(s,a,r,o,t[i+9],5,568446438),o=j(o,s,a,r,t[i+14],9,-1019803690),r=j(r,o,s,a,t[i+3],14,-187363961),a=j(a,r,o,s,t[i+8],20,1163531501),s=j(s,a,r,o,t[i+13],5,-1444681467),o=j(o,s,a,r,t[i+2],9,-51403784),r=j(r,o,s,a,t[i+7],14,1735328473),a=j(a,r,o,s,t[i+12],20,-1926607734),s=K(s,a,r,o,t[i+5],4,-378558),o=K(o,s,a,r,t[i+8],11,-2022574463),r=K(r,o,s,a,t[i+11],16,1839030562),a=K(a,r,o,s,t[i+14],23,-35309556),s=K(s,a,r,o,t[i+1],4,-1530992060),o=K(o,s,a,r,t[i+4],11,1272893353),r=K(r,o,s,a,t[i+7],16,-155497632),a=K(a,r,o,s,t[i+10],23,-1094730640),s=K(s,a,r,o,t[i+13],4,681279174),o=K(o,s,a,r,t[i],11,-358537222),r=K(r,o,s,a,t[i+3],16,-722521979),a=K(a,r,o,s,t[i+6],23,76029189),s=K(s,a,r,o,t[i+9],4,-640364487),o=K(o,s,a,r,t[i+12],11,-421815835),r=K(r,o,s,a,t[i+15],16,530742520),a=K(a,r,o,s,t[i+2],23,-995338651),s=W(s,a,r,o,t[i],6,-198630844),o=W(o,s,a,r,t[i+7],10,1126891415),r=W(r,o,s,a,t[i+14],15,-1416354905),a=W(a,r,o,s,t[i+5],21,-57434055),s=W(s,a,r,o,t[i+12],6,1700485571),o=W(o,s,a,r,t[i+3],10,-1894986606),r=W(r,o,s,a,t[i+10],15,-1051523),a=W(a,r,o,s,t[i+1],21,-2054922799),s=W(s,a,r,o,t[i+8],6,1873313359),o=W(o,s,a,r,t[i+15],10,-30611744),r=W(r,o,s,a,t[i+6],15,-1560198380),a=W(a,r,o,s,t[i+13],21,1309151649),s=W(s,a,r,o,t[i+4],6,-145523070),o=W(o,s,a,r,t[i+11],10,-1120210379),r=W(r,o,s,a,t[i+2],15,718787259),a=W(a,r,o,s,t[i+9],21,-343485551),s=Me(s,l),a=Me(a,u),r=Me(r,m),o=Me(o,d)}return[s,a,r,o]}(function(t){if(t.length===0)return[];const n=8*t.length,s=new Uint32Array(Ta(n));for(let a=0;a<n;a+=8)s[a>>5]|=(255&t[a/8])<<a%32;return s}(e),8*e.length))};tn.default=Ul,Object.defineProperty(en,"__esModule",{value:!0}),en.default=void 0;var Pl=Yr(Oe),Ll=Yr(tn);function Yr(e){return e&&e.__esModule?e:{default:e}}var $l=(0,Pl.default)("v3",48,Ll.default);en.default=$l;var nn={},sn={};Object.defineProperty(sn,"__esModule",{value:!0}),sn.default=void 0;var ql={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};sn.default=ql,Object.defineProperty(nn,"__esModule",{value:!0}),nn.default=void 0;var Ea=jr(sn),Hl=jr(Zt),Gl=Ue;function jr(e){return e&&e.__esModule?e:{default:e}}var Vl=function(e,t,n){if(Ea.default.randomUUID&&!t&&!e)return Ea.default.randomUUID();const s=(e=e||{}).random||(e.rng||Hl.default)();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,t){n=n||0;for(let a=0;a<16;++a)t[n+a]=s[a];return t}return(0,Gl.unsafeStringify)(s)};nn.default=Vl;var an={},rn={};function Bl(e,t,n,s){switch(e){case 0:return t&n^~t&s;case 1:case 3:return t^n^s;case 2:return t&n^t&s^n&s}}function xn(e,t){return e<<t|e>>>32-t}Object.defineProperty(rn,"__esModule",{value:!0}),rn.default=void 0;var Yl=function(e){const t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof e=="string"){const o=unescape(encodeURIComponent(e));e=[];for(let i=0;i<o.length;++i)e.push(o.charCodeAt(i))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);const s=e.length/4+2,a=Math.ceil(s/16),r=new Array(a);for(let o=0;o<a;++o){const i=new Uint32Array(16);for(let l=0;l<16;++l)i[l]=e[64*o+4*l]<<24|e[64*o+4*l+1]<<16|e[64*o+4*l+2]<<8|e[64*o+4*l+3];r[o]=i}r[a-1][14]=8*(e.length-1)/Math.pow(2,32),r[a-1][14]=Math.floor(r[a-1][14]),r[a-1][15]=8*(e.length-1)&4294967295;for(let o=0;o<a;++o){const i=new Uint32Array(80);for(let g=0;g<16;++g)i[g]=r[o][g];for(let g=16;g<80;++g)i[g]=xn(i[g-3]^i[g-8]^i[g-14]^i[g-16],1);let l=n[0],u=n[1],m=n[2],d=n[3],h=n[4];for(let g=0;g<80;++g){const f=Math.floor(g/20),b=xn(l,5)+Bl(f,u,m,d)+h+t[f]+i[g]>>>0;h=d,d=m,m=xn(u,30)>>>0,u=l,l=b}n[0]=n[0]+l>>>0,n[1]=n[1]+u>>>0,n[2]=n[2]+m>>>0,n[3]=n[3]+d>>>0,n[4]=n[4]+h>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]};rn.default=Yl,Object.defineProperty(an,"__esModule",{value:!0}),an.default=void 0;var jl=Kr(Oe),Kl=Kr(rn);function Kr(e){return e&&e.__esModule?e:{default:e}}var Wl=(0,jl.default)("v5",80,Kl.default);an.default=Wl;var on={};Object.defineProperty(on,"__esModule",{value:!0}),on.default=void 0;on.default="00000000-0000-0000-0000-000000000000";var ln={};Object.defineProperty(ln,"__esModule",{value:!0}),ln.default=void 0;var zl=function(e){return e&&e.__esModule?e:{default:e}}(Be),Xl=function(e){if(!(0,zl.default)(e))throw TypeError("Invalid UUID");return parseInt(e.slice(14,15),16)};function jn(e,t){if(!(e&&t&&e.length&&t.length))throw new Error("Bad alphabet");this.srcAlphabet=e,this.dstAlphabet=t}ln.default=Xl,function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"NIL",{enumerable:!0,get:function(){return r.default}}),Object.defineProperty(e,"parse",{enumerable:!0,get:function(){return u.default}}),Object.defineProperty(e,"stringify",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(e,"v1",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(e,"v3",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(e,"v4",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(e,"v5",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(e,"validate",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(e,"version",{enumerable:!0,get:function(){return o.default}});var t=m(Jt),n=m(en),s=m(nn),a=m(an),r=m(on),o=m(ln),i=m(Be),l=m(Ue),u=m(vt);function m(d){return d&&d.__esModule?d:{default:d}}}(Hr),jn.prototype.convert=function(e){var t,n,s,a={},r=this.srcAlphabet.length,o=this.dstAlphabet.length,i=e.length,l=typeof e=="string"?"":[];if(!this.isValid(e))throw new Error('Number "'+e+'" contains of non-alphabetic digits ('+this.srcAlphabet+")");if(this.srcAlphabet===this.dstAlphabet)return e;for(t=0;t<i;t++)a[t]=this.srcAlphabet.indexOf(e[t]);do{for(n=0,s=0,t=0;t<i;t++)(n=n*r+a[t])>=o?(a[s++]=parseInt(n/o,10),n%=o):s>0&&(a[s++]=0);i=s,l=this.dstAlphabet.slice(n,n+1).concat(l)}while(s!==0);return l},jn.prototype.isValid=function(e){for(var t=0;t<e.length;++t)if(this.srcAlphabet.indexOf(e[t])===-1)return!1;return!0};var Jl=jn;function ct(e,t){var n=new Jl(e,t);return function(s){return n.convert(s)}}ct.BIN="01",ct.OCT="01234567",ct.DEC="0123456789",ct.HEX="0123456789abcdef";var Zl=ct;const{v4:An,validate:Ql}=Hr,At=Zl,On={cookieBase90:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!#$%&'()*+-./:<=>?@[]^_`{|}~",flickrBase58:"123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ",uuid25Base36:"0123456789abcdefghijklmnopqrstuvwxyz"},eu={consistentLength:!0};let Mn;const Sa=(e,t,n)=>{const s=t(e.toLowerCase().replace(/-/g,""));return n&&n.consistentLength?s.padStart(n.shortIdLength,n.paddingChar):s},Ia=(e,t)=>{const n=t(e).padStart(32,"0").match(/(\w{8})(\w{4})(\w{4})(\w{4})(\w{12})/);return[n[1],n[2],n[3],n[4],n[5]].join("-")};var tu=(()=>{const e=(t,n)=>{const s=t||On.flickrBase58,a={...eu,...n};if([...new Set(Array.from(s))].length!==s.length)throw new Error("The provided Alphabet has duplicate characters resulting in unreliable results");const r=(o=s.length,Math.ceil(Math.log(2**128)/Math.log(o)));var o;const i={shortIdLength:r,consistentLength:a.consistentLength,paddingChar:s[0]},l=At(At.HEX,s),u=At(s,At.HEX),m=()=>Sa(An(),l,i),d={alphabet:s,fromUUID:h=>Sa(h,l,i),maxLength:r,generate:m,new:m,toUUID:h=>Ia(h,u),uuid:An,validate:(h,g=!1)=>{if(!h||typeof h!="string")return!1;const f=a.consistentLength?h.length===r:h.length<=r,b=h.split("").every(y=>s.includes(y));return g===!1?f&&b:f&&b&&Ql(Ia(h,u))}};return Object.freeze(d),d};return e.constants=On,e.uuid=An,e.generate=()=>(Mn||(Mn=e(On.flickrBase58).generate),Mn()),e})();const nu=po(tu),Wr={[oe.NOT_STARTED]:"[ ]",[oe.IN_PROGRESS]:"[/]",[oe.COMPLETE]:"[x]",[oe.CANCELLED]:"[-]"},zr=nu(void 0,{consistentLength:!0});function su(e,t){if(e.uuid===t)return e;if(e.subTasksData)for(const n of e.subTasksData){const s=su(n,t);if(s)return s}}function Xr(e,t={}){const{shallow:n=!1,excludeUuid:s=!1,shortUuid:a=!0}=t;return Jr(e,{shallow:n,excludeUuid:s,shortUuid:a}).join(`
`)}function Jr(e,t={}){const{shallow:n=!1,excludeUuid:s=!1,shortUuid:a=!0}=t;let r="";s||(r=`UUID:${a?function(i){try{return zr.fromUUID(i)}catch{return i}}(e.uuid):e.uuid} `);const o=`${Wr[e.state]} ${r}NAME:${e.name} DESCRIPTION:${e.description}`;return n||!e.subTasksData||e.subTasksData.length===0?[o]:[o,...(e.subTasksData||[]).map(i=>Jr(i,t).map(l=>`-${l}`)).flat()]}function au(e,t){var s;const n=(s=e.subTasksData)==null?void 0:s.map(a=>au(a,t));return{...e,uuid:t!=null&&t.keepUuid?e.uuid:crypto.randomUUID(),subTasks:(n==null?void 0:n.map(a=>a.uuid))||[],subTasksData:n}}function yc(e,t={}){if(!e.trim())throw new Error("Empty markdown");const n=e.split(`
`);let s=0;for(const u of n)if(u.trim()&&Na(u)===0)try{Kn(u,t),s++}catch{}if(s===0)throw new Error("No root task found");if(s>1)throw new Error(`Multiple root tasks found (${s}). There can only be one root task per conversation. All other tasks must be subtasks (indented with dashes). Root task format: [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (no dashes). Subtask format: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (with dashes).`);const a=e.split(`
`);function r(){for(;a.length>0;){const u=a.shift(),m=Na(u);try{return{task:Kn(u,t),level:m}}catch{}}}const o=r();if(!o)throw new Error("No root task found");const i=[o.task];let l;for(;l=r();){const u=i[l.level-1];if(!u)throw new Error(`Invalid markdown: level ${l.level+1} has no parent
Line: ${l.task.name} is missing a parent
Current tasks: 
${Xr(o.task)}`);u.subTasksData&&u.subTasks||(u.subTasks=[],u.subTasksData=[]),u.subTasksData.push(l.task),u.subTasks.push(l.task.uuid),i[l.level]=l.task,i.splice(l.level+1)}return o.task}function Na(e){let t=0,n=0;for(;n<e.length&&(e[n]===" "||e[n]==="	");)e[n]===" "?t+=.5:e[n]==="	"&&(t+=1),n++;for(;n<e.length&&e[n]==="-";)t+=1,n++;return Math.floor(t)}function Kn(e,t={}){const{excludeUuid:n=!1,shortUuid:s=!0}=t;let a=0;for(;a<e.length&&(e[a]===" "||e[a]==="	"||e[a]==="-");)a++;const r=e.substring(a),o=r.match(/^\s*\[([ x\-/?])\]/);if(!o)throw new Error(`Invalid task line: ${e} (missing state)`);const i=o[1],l=Object.entries(Wr).reduce((g,[f,b])=>(g[b.substring(1,2)]=f,g),{})[i]||oe.NOT_STARTED,u=r.substring(o.index+o[0].length).trim();let m,d,h;if(n){const g=/(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,f=u.match(g);if(!f){const b=/\b(?:name|NAME):/i.test(u),y=/\b(?:description|DESCRIPTION):/i.test(u);throw!b||!y?new Error(`Invalid task line: ${e} (missing required fields)`):u.toLowerCase().indexOf("name:")<u.toLowerCase().indexOf("description:")?new Error(`Invalid task line: ${e} (invalid format)`):new Error(`Invalid task line: ${e} (incorrect field order)`)}if(d=f[1].trim(),h=f[2].trim(),!d)throw new Error(`Invalid task line: ${e} (missing required fields)`);m=crypto.randomUUID()}else{const g=/(?:uuid|UUID):([^]*?)(?=(?:name|NAME):)(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,f=u.match(g);if(!f){const b=/\b(?:uuid|UUID):/i.test(u),y=/\b(?:name|NAME):/i.test(u),v=/\b(?:description|DESCRIPTION):/i.test(u);if(!b||!y||!v)throw new Error(`Invalid task line: ${e} (missing required fields)`);const C=u.toLowerCase().indexOf("uuid:"),N=u.toLowerCase().indexOf("name:"),P=u.toLowerCase().indexOf("description:");throw C<N&&N<P?new Error(`Invalid task line: ${e} (invalid format)`):new Error(`Invalid task line: ${e} (incorrect field order)`)}if(m=f[1].trim(),d=f[2].trim(),h=f[3].trim(),!m||!d)throw new Error(`Invalid task line: ${e} (missing required fields)`);if(m==="NEW_UUID")m=crypto.randomUUID();else if(s)try{m=function(b){try{return zr.toUUID(b)}catch{return b}}(m)}catch{}}return{uuid:m,name:d,description:h,state:l,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:ys.USER}}const rt=e=>({uuid:crypto.randomUUID(),name:"New Task",description:"New task description",state:oe.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:ys.USER,...e}),wa=rt({name:"Task 1.1",description:"This is the first sub task",state:oe.IN_PROGRESS}),Ca=rt({name:"Task 1.2.1",description:"This is a nested sub task, child of Task 1.2",state:oe.NOT_STARTED}),ka=rt({name:"Task 1.2.2",description:"This is another nested sub task, child of Task 1.2",state:oe.IN_PROGRESS}),Ra=rt({name:"Task 1.2",description:"This is the second sub task",state:oe.COMPLETE,subTasks:[Ca.uuid,ka.uuid],subTasksData:[Ca,ka]}),xa=rt({name:"Task 1.3",description:"This is the third sub task",state:oe.CANCELLED}),bc=Xr(rt({name:"Task 1",description:"This is the first task",state:oe.NOT_STARTED,subTasks:[wa.uuid,Ra.uuid,xa.uuid],subTasksData:[wa,Ra,xa]}));function Zr(e){const t=e.split(`
`);let n=null;const s={created:[],updated:[],deleted:[]};for(const a of t){const r=a.trim();if(r!=="## Created Tasks")if(r!=="## Updated Tasks")if(r!=="## Deleted Tasks"){if(n&&(r.startsWith("[ ]")||r.startsWith("[/]")||r.startsWith("[x]")||r.startsWith("[-]")))try{const o=Kn(r,{excludeUuid:!1,shortUuid:!0});o&&s[n].push(o)}catch{}}else n="deleted";else n="updated";else n="created"}return s}function _c(e){const t=e.match(/Created: (\d+), Updated: (\d+), Deleted: (\d+)/);if(t)return{created:parseInt(t[1],10),updated:parseInt(t[2],10),deleted:parseInt(t[3],10)};const n=Zr(Qr(e));return{created:n.created.length,updated:n.updated.length,deleted:n.deleted.length}}function Qr(e){const t=e.indexOf("# Task Changes");if(t===-1)return"";const n=e.substring(t),s=[`
New and Updated Tasks:`,`
Remember:`,`

---`];let a=n.length;for(const i of s){const l=n.indexOf(i);l!==-1&&l<a&&(a=l)}const r=n.substring(0,a),o=r.indexOf(`
`);return o===-1?"":r.substring(o+1).trim()}function vc(e){return Zr(Qr(e))}class ru{static getTaskOrchestratorPrompt(t){const{taskTree:n,surroundingContext:s}=t,a=this.buildTaskContext(n,s);return`Please utilize sub-agents to complete the following task tree.
Here are the details, along with a suggestion prompt.
You may use 1 or more sub-agents in to complete the below task.
For each sub-agent, please give it the relevant context and breakdown of the below task.

## Task Details
**Name:** ${n.name}
${n.description?`**Description:** ${n.description}`:""}
**Status:** ${n.state}

## Task Context
${a}

## Instructions
Please complete this task according to the requirements.
When you are done, report back on the completion status with a summary of changes made,
important context, and other relevant information for the supervisor.

Focus on this specific task tree while being aware of the broader context provided above.`}static getTaskMentionId(t){return`task:${t.taskUuid}:${t.taskTree.name.replace(/\s+/g,"_")}`}static getTaskMentionLabel(t){const{taskTree:n,surroundingContext:s}=t;return s.targetTaskPath.length>1?`${s.targetTaskPath.slice(0,-1).join(" → ")} → ${n.name}`:n.name}static buildTaskContext(t,n){const{rootTask:s,targetTaskPath:a}=n;let r=`This task is part of a larger project: "${s.name}"`;return s.description&&(r+=`

**Project Description:** ${s.description}`),a.length>1&&(r+=`

**Task Path:** ${a.join(" → ")}`),t.subTasksData&&t.subTasksData.length>0&&(r+=`

**Subtasks:**`,t.subTasksData.forEach((o,i)=>{r+=`
${i+1}. ${o.name} (${o.state})`,o.description&&(r+=` - ${o.description}`)})),r}}function it(e){var t;return((t=e.extraData)==null?void 0:t.isAgentConversation)===!0}var ou=(e=>(e[e.active=0]="active",e[e.inactive=1]="inactive",e))(ou||{});class iu{constructor(){this._controllers=new Set,this._timeoutIds=new Set}addCallback(t,n){const s=new AbortController,a=setTimeout(()=>{t(s.signal),this._controllers.delete(s),this._timeoutIds.delete(a)},n);this._controllers.add(s),this._timeoutIds.add(a)}cancelAll(){this._controllers.forEach(t=>t.abort()),this._timeoutIds.forEach(t=>clearTimeout(t)),this._controllers.clear(),this._timeoutIds.clear()}}function Dn(e){return e.reduce((t,n)=>t+eo(n),0)}function eo(e){let t=0;return e.request_nodes?t+=JSON.stringify(e.request_nodes).length:t+=(e.request_message||"").length,e.response_nodes?t+=JSON.stringify(e.response_nodes).length:t+=(e.response_text||"").length,t}const de={triggerOnHistorySizeChars:0,historyTailSizeCharsToExclude:0,triggerOnHistorySizeCharsWhenCacheExpiring:0,prompt:"",cacheTTLMs:0,bufferTimeBeforeCacheExpirationMs:0,summaryNodeRequestMessageTemplate:`
<supervisor>
Conversation history between Agent(you) and the user and history of tool calls was summarized to reduce context size.
Summary was generated by Agent(you) so 'I' in the summary represents Agent(you).
Here is the summary:
<summary>
{summary}
</summary>
Continue the conversation and finish the task given by the user from this point.
</supervisor>`,summaryNodeResponseMessage:"Ok. I will continue the conversation from this point."};class lu{constructor(t,n,s){c(this,"historySummaryVersion",2);c(this,"_callbacksManager",new iu);c(this,"_params");this._conversationModel=t,this._extensionClient=n,this._chatFlagModel=s,this._params=Aa(s.historySummaryParams),s.subscribe(a=>{this._params=Aa(a.historySummaryParams)})}cancelRunningOrScheduledSummarizations(){this._callbacksManager.cancelAll()}clearStaleHistorySummaryNodes(t){return t.filter(n=>!Je(n)||n.summaryVersion===this.historySummaryVersion)}maybeScheduleSummarization(t){if(!this._chatFlagModel.useHistorySummary||this._params.triggerOnHistorySizeCharsWhenCacheExpiring<=0)return;const n=this._params.cacheTTLMs-t-this._params.bufferTimeBeforeCacheExpirationMs;n>0&&this._callbacksManager.addCallback(s=>{this.maybeAddHistorySummaryNode(!0,s)},n)}preprocessChatHistory(t){const n=t.findLastIndex(s=>Je(s)&&s.summaryVersion===this.historySummaryVersion);return this._chatFlagModel.useHistorySummary?(n>0&&(console.info(`Using history summary node found at index ${n} with requestId: ${t[n].request_id}`),t=t.slice(n)),t=t.filter(s=>!Je(s)||s.summaryVersion===this.historySummaryVersion)):t=t.filter(s=>!Je(s)),t}async maybeAddHistorySummaryNode(t=!1,n){var R,te,ne;if(console.log("maybeAddHistorySummaryNode. isCacheAboutToExpire: ",t),!this._params.prompt||this._params.prompt.trim()==="")return console.log("maybeAddHistorySummaryNode. empty prompt"),!1;const s=this._conversationModel.convertHistoryToExchanges(this._conversationModel.chatHistory),a=t?this._params.triggerOnHistorySizeCharsWhenCacheExpiring:this._params.triggerOnHistorySizeChars;if(console.log("maybeAddHistorySummaryNode. maxCharsThreshold: ",a),a<=0)return!1;const{head:r,tail:o,headSizeChars:i,tailSizeChars:l}=function(T,Ie,Ye,w){if(T.length===0)return{head:[],tail:[],headSizeChars:0,tailSizeChars:0};const ue=[],U=[];let ce=0,Ne=0,St=0;for(let mn=T.length-1;mn>=0;mn--){const hn=T[mn],It=eo(hn);ce+It<Ie||U.length<w?(U.push(hn),St+=It):(ue.push(hn),Ne+=It),ce+=It}return ce<Ye?(U.push(...ue),{head:[],tail:U.reverse(),headSizeChars:0,tailSizeChars:ce}):{head:ue.reverse(),tail:U.reverse(),headSizeChars:Ne,tailSizeChars:St}}(s,this._params.historyTailSizeCharsToExclude,a,1);if(console.log("maybeAddHistorySummaryNode. headSizeChars: ",i," tailSizeChars: ",l),r.length===0)return console.log("maybeAddHistorySummaryNode. head is empty. nothing to summarize"),!1;const u=Dn(s),m=Dn(r),d=Dn(o),h={totalHistoryCharCount:u,totalHistoryExchangeCount:s.length,headCharCount:m,headExchangeCount:r.length,headLastRequestId:((R=r.at(-1))==null?void 0:R.request_id)??"",tailCharCount:d,tailExchangeCount:o.length,tailLastRequestId:((te=o.at(-1))==null?void 0:te.request_id)??"",summaryCharCount:0,summarizationDurationMs:0,isCacheAboutToExpire:t,isAborted:!1};let g=((ne=r.at(-1))==null?void 0:ne.response_nodes)??[],f=g.filter(T=>T.type===D.TOOL_USE);f.length>0&&(r.at(-1).response_nodes=g.filter(T=>T.type!==D.TOOL_USE)),console.info("Summarizing %d turns of conversation history.",r.length);const b=Date.now(),{responseText:y,requestId:v}=await this._conversationModel.sendSilentExchange({request_message:this._params.prompt,disableRetrieval:!0,disableSelectedCodeDetails:!0,chatHistory:r}),C=Date.now();if(h.summaryCharCount=y.length,h.summarizationDurationMs=C-b,h.isAborted=!!(n!=null&&n.aborted),this._extensionClient.reportAgentRequestEvent({eventName:zt.chatHistorySummarization,conversationId:this._conversationModel.conversationId,requestId:v??"UNKNOWN_REQUEST_ID",chatHistoryLength:this._conversationModel.chatHistory.length,eventData:{chatHistorySummarizationData:h}}),n==null?void 0:n.aborted)return console.log("maybeAddHistorySummaryNode. aborted"),!1;if(!v||y.trim()==="")return console.log("maybeAddHistorySummaryNode. no request id or empty response"),!1;const N=this._params.summaryNodeRequestMessageTemplate.replace("{summary}",y),P=this._params.summaryNodeResponseMessage,S={chatItemType:Xe.historySummary,summaryVersion:this.historySummaryVersion,request_id:v,request_message:N,response_text:P,structured_output_nodes:[{id:f.map(T=>T.id).reduce((T,Ie)=>Math.max(T,Ie),-1)+1,type:D.RAW_RESPONSE,content:P},...f],status:E.success,seen_state:z.seen,timestamp:new Date().toISOString()},k=this._conversationModel.chatHistory.findLastIndex(T=>T.request_id===r.at(-1).request_id)+1;return console.info("Adding a history summary node at index %d",k),this._conversationModel.insertChatItem(k,S),!0}}function Aa(e){try{if(!e)return console.log("historySummaryParams is empty. Using default params"),de;const t=JSON.parse(e),n={triggerOnHistorySizeChars:t.trigger_on_history_size_chars||de.triggerOnHistorySizeChars,historyTailSizeCharsToExclude:t.history_tail_size_chars_to_exclude||de.historyTailSizeCharsToExclude,triggerOnHistorySizeCharsWhenCacheExpiring:t.trigger_on_history_size_chars_when_cache_expiring||de.triggerOnHistorySizeCharsWhenCacheExpiring,prompt:t.prompt||de.prompt,cacheTTLMs:t.cache_ttl_ms||de.cacheTTLMs,bufferTimeBeforeCacheExpirationMs:t.buffer_time_before_cache_expiration_ms||de.bufferTimeBeforeCacheExpirationMs,summaryNodeRequestMessageTemplate:t.summary_node_request_message_template||de.summaryNodeRequestMessageTemplate,summaryNodeResponseMessage:t.summary_node_response_message||de.summaryNodeResponseMessage};n.summaryNodeRequestMessageTemplate.includes("{summary}")||(console.error("summaryNodeRequestMessageTemplate must contain {summary}. Using default template"),n.summaryNodeRequestMessageTemplate=de.summaryNodeRequestMessageTemplate);const s={...n,prompt:n.prompt.slice(0,10)+"..."};return console.log("historySummaryParams updated: ",s),n}catch(t){return console.error("Failed to parse history_summary_params:",t),de}}const uu=new Error("request for lock canceled");var cu=function(e,t,n,s){return new(n||(n=Promise))(function(a,r){function o(u){try{l(s.next(u))}catch(m){r(m)}}function i(u){try{l(s.throw(u))}catch(m){r(m)}}function l(u){var m;u.done?a(u.value):(m=u.value,m instanceof n?m:new n(function(d){d(m)})).then(o,i)}l((s=s.apply(e,t||[])).next())})};class du{constructor(t,n=uu){this._value=t,this._cancelError=n,this._queue=[],this._weightedWaiters=[]}acquire(t=1,n=0){if(t<=0)throw new Error(`invalid weight ${t}: must be positive`);return new Promise((s,a)=>{const r={resolve:s,reject:a,weight:t,priority:n},o=Oa(this._queue,i=>n<=i.priority);o===-1&&t<=this._value?this._dispatchItem(r):this._queue.splice(o+1,0,r)})}runExclusive(t){return cu(this,arguments,void 0,function*(n,s=1,a=0){const[r,o]=yield this.acquire(s,a);try{return yield n(r)}finally{o()}})}waitForUnlock(t=1,n=0){if(t<=0)throw new Error(`invalid weight ${t}: must be positive`);return this._couldLockImmediately(t,n)?Promise.resolve():new Promise(s=>{this._weightedWaiters[t-1]||(this._weightedWaiters[t-1]=[]),function(a,r){const o=Oa(a,i=>r.priority<=i.priority);a.splice(o+1,0,r)}(this._weightedWaiters[t-1],{resolve:s,priority:n})})}isLocked(){return this._value<=0}getValue(){return this._value}setValue(t){this._value=t,this._dispatchQueue()}release(t=1){if(t<=0)throw new Error(`invalid weight ${t}: must be positive`);this._value+=t,this._dispatchQueue()}cancel(){this._queue.forEach(t=>t.reject(this._cancelError)),this._queue=[]}_dispatchQueue(){for(this._drainUnlockWaiters();this._queue.length>0&&this._queue[0].weight<=this._value;)this._dispatchItem(this._queue.shift()),this._drainUnlockWaiters()}_dispatchItem(t){const n=this._value;this._value-=t.weight,t.resolve([n,this._newReleaser(t.weight)])}_newReleaser(t){let n=!1;return()=>{n||(n=!0,this.release(t))}}_drainUnlockWaiters(){if(this._queue.length===0)for(let t=this._value;t>0;t--){const n=this._weightedWaiters[t-1];n&&(n.forEach(s=>s.resolve()),this._weightedWaiters[t-1]=[])}else{const t=this._queue[0].priority;for(let n=this._value;n>0;n--){const s=this._weightedWaiters[n-1];if(!s)continue;const a=s.findIndex(r=>r.priority<=t);(a===-1?s:s.splice(0,a)).forEach(r=>r.resolve())}}}_couldLockImmediately(t,n){return(this._queue.length===0||this._queue[0].priority<n)&&t<=this._value}}function Oa(e,t){for(let n=e.length-1;n>=0;n--)if(t(e[n]))return n;return-1}var mu=function(e,t,n,s){return new(n||(n=Promise))(function(a,r){function o(u){try{l(s.next(u))}catch(m){r(m)}}function i(u){try{l(s.throw(u))}catch(m){r(m)}}function l(u){var m;u.done?a(u.value):(m=u.value,m instanceof n?m:new n(function(d){d(m)})).then(o,i)}l((s=s.apply(e,t||[])).next())})};class hu{constructor(t){this._semaphore=new du(1,t)}acquire(){return mu(this,arguments,void 0,function*(t=0){const[,n]=yield this._semaphore.acquire(1,t);return n})}runExclusive(t,n=0){return this._semaphore.runExclusive(()=>t(),1,n)}isLocked(){return this._semaphore.isLocked()}waitForUnlock(t=0){return this._semaphore.waitForUnlock(1,t)}release(){this._semaphore.isLocked()&&this._semaphore.release()}cancel(){return this._semaphore.cancel()}}const Gt="temp-fe";class se{constructor(t,n,s,a,r){c(this,"_state");c(this,"_subscribers",new Set);c(this,"_focusModel",new Oi);c(this,"_onSendExchangeListeners",[]);c(this,"_onNewConversationListeners",[]);c(this,"_onHistoryDeleteListeners",[]);c(this,"_onBeforeChangeConversationListeners",[]);c(this,"_totalCharactersCacheThrottleMs",1e3);c(this,"_sendUserMessageMutex",new hu);c(this,"_totalCharactersStore");c(this,"_chatHistorySummarizationModel");c(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));c(this,"setConversation",(t,n=!0,s=!0)=>{const a=t.id!==this._state.id;a&&s&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([o,i])=>{if(i.requestId&&i.toolUseId){const{requestId:l,toolUseId:u}=ba(o);return l===i.requestId&&u===i.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",o,"but object has ",wn(i)),[o,i]}return[o,{...i,...ba(o)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),n&&a&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const r=se.isEmpty(t);if(a&&r){const o=this._state.draftExchange;o&&(t.draftExchange=o)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter(L)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(o=>o(this)),this._saveConversation(this._state),a&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(o=>o())),!0});c(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});c(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});c(this,"setName",t=>{this.update({name:t})});c(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});c(this,"updateFeedback",(t,n)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:n}})});c(this,"updateToolUseState",t=>{this.update({toolUseStates:{...this._state.toolUseStates,[wn(t)]:t}})});c(this,"getToolUseState",(t,n)=>t===void 0||n===void 0||this.toolUseStates===void 0?{phase:x.unknown,requestId:t??"",toolUseId:n??""}:this.toolUseStates[wn({requestId:t,toolUseId:n})]||{phase:x.new});c(this,"getLastToolUseId",()=>{var s,a;const t=this.lastExchange;if(!t)return;const n=(((s=t==null?void 0:t.structured_output_nodes)==null?void 0:s.filter(r=>r.type===D.TOOL_USE))??[]).at(-1);return n?(a=n.tool_use)==null?void 0:a.tool_use_id:void 0});c(this,"getLastToolUseState",()=>{var s;const t=this.lastExchange;if(!t)return{phase:x.unknown};const n=function(a=[]){let r;for(const o of a){if(o.type===D.TOOL_USE)return o;o.type===D.TOOL_USE_START&&(r=o)}return r}(t==null?void 0:t.structured_output_nodes);return n?this.getToolUseState(t.request_id,(s=n.tool_use)==null?void 0:s.tool_use_id):{phase:x.unknown}});c(this,"addExchange",(t,n)=>{const s=this._state.chatHistory;let a,r;a=n===void 0?[...s,t]:n===-1?s.length===0?[t]:[...s.slice(0,-1),t,s[s.length-1]]:[...s.slice(0,n),t,...s.slice(n)],L(t)&&(r=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:$r.unset,feedbackNote:""}}:void 0),this.update({chatHistory:a,...r?{feedbackStates:r}:{},lastUrl:void 0})});c(this,"addExchangeBeforeLast",t=>{this.addExchange(t,-1)});c(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});c(this,"updateExchangeById",(t,n,s=!1)=>{var i;const a=this.exchangeWithRequestId(n);if(a===null)return console.warn("No exchange with this request ID found."),!1;s&&t.response_text!==void 0&&(t.response_text=(a.response_text??"")+(t.response_text??"")),s&&(t.structured_output_nodes=function(l=[]){const u=bl(l);return u&&u.type===D.TOOL_USE?l.filter(m=>m.type!==D.TOOL_USE_START):l}([...a.structured_output_nodes??[],...t.structured_output_nodes??[]])),t.stop_reason!==a.stop_reason&&a.stop_reason&&t.stop_reason===uo.REASON_UNSPECIFIED&&(t.stop_reason=a.stop_reason),s&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...a.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const r=(i=(t.structured_output_nodes||[]).find(l=>l.type===D.MAIN_TEXT_FINISHED))==null?void 0:i.content;r&&r!==t.response_text&&(t.response_text=r);let o=this._state.isShareable||Fn({...a,...t});return this.update({chatHistory:this.chatHistory.map(l=>l.request_id===n?{...l,...t}:l),isShareable:o}),!0});c(this,"clearMessagesFromHistory",t=>{const n=this._collectToolUseIdsFromMessages(this.chatHistory.filter(s=>s.request_id&&t.has(s.request_id)));this.update({chatHistory:this.chatHistory.filter(s=>!s.request_id||!t.has(s.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(t),toolUseIds:n})});c(this,"clearHistory",()=>{const t=this._collectToolUseIdsFromMessages(this.chatHistory);this._extensionClient.clearMetadataFor({requestIds:this.requestIds,toolUseIds:t}),this.update({chatHistory:[]})});c(this,"clearHistoryFrom",async(t,n=!0)=>{const s=this.historyFrom(t,n),a=s.map(o=>o.request_id).filter(o=>o!==void 0),r=this._collectToolUseIdsFromMessages(s);this.update({chatHistory:this.historyTo(t,!n)}),this._extensionClient.clearMetadataFor({requestIds:a,toolUseIds:r}),s.forEach(o=>{this._onHistoryDeleteListeners.forEach(i=>i(o))})});c(this,"clearMessageFromHistory",t=>{const n=this.chatHistory.find(a=>a.request_id===t),s=n?this._collectToolUseIdsFromMessages([n]):[];this.update({chatHistory:this.chatHistory.filter(a=>a.request_id!==t)}),this._extensionClient.clearMetadataFor({requestIds:[t],toolUseIds:s})});c(this,"_collectToolUseIdsFromMessages",t=>{var s;const n=[];for(const a of t)if(L(a)&&a.structured_output_nodes)for(const r of a.structured_output_nodes)r.type===D.TOOL_USE&&((s=r.tool_use)!=null&&s.tool_use_id)&&n.push(r.tool_use.tool_use_id);return n});c(this,"historyTo",(t,n=!1)=>{const s=this.chatHistory.findIndex(a=>a.request_id===t);return s===-1?[]:this.chatHistory.slice(0,n?s+1:s)});c(this,"historyFrom",(t,n=!0)=>{const s=this.chatHistory.findIndex(a=>a.request_id===t);return s===-1?[]:this.chatHistory.slice(n?s:s+1)});c(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});c(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:E.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,model_id:t.model_id},!1,t.request_id)));c(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(n=>n!==t&&(!t.request_id||n.request_id!==t.request_id))})});c(this,"exchangeWithRequestId",t=>this.chatHistory.find(n=>n.request_id===t)||null);c(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});c(this,"markSeen",async t=>{if(!t.request_id||!this.chatHistory.find(s=>s.request_id===t.request_id))return;const n={seen_state:z.seen};this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t.request_id?{...s,...n}:s)})});c(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));c(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const n=t.filter(s=>!s.personality&&!s.task);this.update({draftExchange:{...this.draftExchange,mentioned_items:n}})});c(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(n=>n.id);this.update({draftActiveContextIds:t})});c(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),n=this._specialContextInputModel.recentItems.filter(a=>t.has(a.id)||a.recentFile||a.selection||a.sourceFolder),s=this._specialContextInputModel.recentItems.filter(a=>!(t.has(a.id)||a.recentFile||a.selection||a.sourceFolder));this._specialContextInputModel.markItemsActive(n.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});c(this,"saveDraftExchange",(t,n)=>{var o,i,l;const s=t!==((o=this.draftExchange)==null?void 0:o.request_message),a=n!==((i=this.draftExchange)==null?void 0:i.rich_text_json_repr);if(!s&&!a)return;const r=(l=this.draftExchange)==null?void 0:l.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:n,mentioned_items:r,status:E.draft}})});c(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});c(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const n=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:n,model_id:this.selectedModelId??void 0}).then(()=>{var s;if(!it(this)){const a=!this.name&&this.chatHistory.length===1&&((s=this.firstExchange)==null?void 0:s.request_id)===this.chatHistory[0].request_id;this._chatFlagModel.summaryTitles&&a&&this.updateConversationTitle()}}).finally(()=>{var s;it(this)&&this._extensionClient.reportAgentRequestEvent({eventName:zt.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});c(this,"cancelMessage",async()=>{var t;this.canCancelMessage&&((t=this.lastExchange)!=null&&t.request_id)&&(this.updateExchangeById({status:E.cancelled},this.lastExchange.request_id),await this._extensionClient.cancelChatStream(this.lastExchange.request_id))});c(this,"sendInstructionExchange",async(t,n)=>{let s=`${Gt}-${crypto.randomUUID()}`;const a={status:E.sent,request_id:s,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:z.unseen,timestamp:new Date().toISOString()};this.addExchange(a);for await(const r of this._extensionClient.sendInstructionMessage(a,n)){if(!this.updateExchangeById(r,s,!0))return;s=r.request_id||s}});c(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});c(this,"checkAndGenerateAgentTitle",()=>{var n;if(!(!it(this)||!this._chatFlagModel.summaryTitles||this.name)){var t;!this.name&&(t=this.chatHistory,t.filter(s=>Yn(s))).length===1&&!((n=this.extraData)!=null&&n.hasTitleGenerated)&&(this.update({extraData:{...this.extraData,hasTitleGenerated:!0}}),this.updateConversationTitle())}});c(this,"sendSummaryExchange",()=>{const t={status:E.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:Xe.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});c(this,"generateCommitMessage",async()=>{let t=`${Gt}-${crypto.randomUUID()}`;const n={status:E.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:z.unseen,chatItemType:Xe.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(n);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,t,!0))return;t=s.request_id||t}});c(this,"sendExchange",async(t,n=!1,s)=>{var m;this._chatHistorySummarizationModel.cancelRunningOrScheduledSummarizations(),this.updateLastInteraction();let a=`${Gt}-${crypto.randomUUID()}`,r=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;if(se.isNew(this._state)){const d=crypto.randomUUID(),h=this._state.id;try{await this._extensionClient.migrateConversationId(h,d)}catch(g){console.error("Failed to migrate conversation checkpoints:",g)}this._state={...this._state,id:d},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(d),this._subscribers.forEach(g=>g(this))}t=Da(t);let o={status:E.sent,request_id:a,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:r,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:z.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(o),this._loadContextFromExchange(o),this._onSendExchangeListeners.forEach(d=>d(o)),this._chatFlagModel.useHistorySummary&&!t.request_message&&await this._chatHistorySummarizationModel.maybeAddHistorySummaryNode()&&this.update({chatHistory:this._chatHistorySummarizationModel.clearStaleHistorySummaryNodes(this.chatHistory)}),o=await this._addIdeStateNode(o),this.updateExchangeById({structured_request_nodes:o.structured_request_nodes},a,!1);const i=Date.now();let l=!1;for await(const d of this.sendUserMessage(a,o,n,s)){if(((m=this.exchangeWithRequestId(a))==null?void 0:m.status)!==E.sent||!this.updateExchangeById(d,a,!0))return;if(a=d.request_id||a,!l&&it(this)){const h=Date.now(),g=h-i;this._extensionClient.reportAgentRequestEvent({eventName:zt.firstTokenReceived,conversationId:this.id,requestId:a,chatHistoryLength:this.chatHistory.length,eventData:{firstTokenTimingData:{userMessageSentTimestampMs:i,firstTokenReceivedTimestampMs:h,timeToFirstTokenMs:g}}}),l=!0}}const u=Date.now()-i;this._chatHistorySummarizationModel.maybeScheduleSummarization(u)});c(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:E.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(Fr.chatUseSuggestedQuestion)});c(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});c(this,"recoverExchange",async t=>{var a;if(!t.request_id||t.status!==E.sent)return;let n=t.request_id;const s=(a=t.structured_output_nodes)==null?void 0:a.filter(r=>r.type===D.AGENT_MEMORY);this.updateExchangeById({...t,response_text:t.lastChunkId?t.response_text:"",structured_output_nodes:t.lastChunkId?t.structured_output_nodes??[]:s},n);for await(const r of this.getChatStream(t)){if(!this.updateExchangeById(r,n,!0))return;n=r.request_id||n}});c(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(n=>{L(n)&&this._loadContextFromExchange(n)})});c(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});c(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(n=>{L(n)&&this._unloadContextFromExchange(n)})});c(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});c(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});c(this,"_jsonToStructuredRequest",t=>{const n=[],s=r=>{var i;const o=n.at(-1);if((o==null?void 0:o.type)===G.TEXT){const l=((i=o.text_node)==null?void 0:i.content)??"",u={...o,text_node:{content:l+r}};n[n.length-1]=u}else n.push({id:n.length,type:G.TEXT,text_node:{content:r}})},a=r=>{var o,i,l,u,m;if(r.type==="doc"||r.type==="paragraph")for(const d of r.content??[])a(d);else if(r.type==="hardBreak")s(`
`);else if(r.type==="text")s(r.text??"");else if(r.type==="file"){if(typeof((o=r.attrs)==null?void 0:o.src)!="string")return void console.error("File source is not a string: ",(i=r.attrs)==null?void 0:i.src);if(r.attrs.isLoading)return;const d=(l=r.attrs)==null?void 0:l.title,h=mo(d);ho(d)?n.push({id:n.length,type:G.IMAGE_ID,image_id_node:{image_id:r.attrs.src,format:h}}):n.push({id:n.length,type:G.FILE_ID,file_id_node:{file_id:r.attrs.src,file_name:d}})}else if(r.type==="mention"){const d=(u=r.attrs)==null?void 0:u.data;d&&qr(d)?n.push({id:n.length,type:G.TEXT,text_node:{content:El(this._chatFlagModel,d.personality.type)}}):d&&ul(d)?n.push({id:n.length,type:G.TEXT,text_node:{content:ru.getTaskOrchestratorPrompt(d.task)}}):s(`@\`${(d==null?void 0:d.name)??(d==null?void 0:d.id)}\``)}else if(r.type==="askMode"){const d=(m=r.attrs)==null?void 0:m.prompt;d&&n.push({id:n.length,type:G.TEXT,text_node:{content:d}})}};return a(t),n});this._extensionClient=t,this._chatFlagModel=n,this._specialContextInputModel=s,this._saveConversation=a,this._state={...se.create(r!=null&&r.forceAgentConversation?{extraData:{isAgentConversation:!0,hasAgentOnboarded:!0}}:void 0)},this._totalCharactersStore=this._createTotalCharactersStore(),this._chatHistorySummarizationModel=new lu(this,t,n)}get conversationId(){return this._state.id}insertChatItem(t,n){const s=[...this._state.chatHistory];s.splice(t,0,n),this.update({chatHistory:s})}_createTotalCharactersStore(){return Zi(()=>{let t=0;const n=this._state.chatHistory;return this.convertHistoryToExchanges(n).forEach(s=>{t+=JSON.stringify(s).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((s,a)=>s+a,0))||0)<=4?B.PROTOTYPER:B.DEFAULT}catch(n){return console.error("Error determining persona type:",n),B.DEFAULT}}static create(t={}){const n=new Date().toISOString();return{id:t.id||crypto.randomUUID(),name:void 0,createdAtIso:n,lastInteractedAtIso:n,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:B.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){if(t.name)return t.name;const n=t.chatHistory.find(L);return n&&n.request_message?se.toSentenceCase(n.request_message):it(t)?"New Agent":"New Chat"}static isNew(t){return t.id===vl}static isEmpty(t){var a;const n=t.chatHistory.filter(r=>L(r)),s=t.chatHistory.filter(r=>gu(r));return n.length===0&&s.length===0&&!((a=t.draftExchange)!=null&&a.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static getTime(t,n){return n==="lastMessageTimestamp"?se.lastMessageTimestamp(t):n==="lastInteractedAt"?se.lastInteractedAt(t):se.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){var s;const n=(s=t.chatHistory.findLast(L))==null?void 0:s.timestamp;return n?new Date(n):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!se.isEmpty(t)||se.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(n=>n!==t)}}_notifyBeforeChangeConversation(t,n){let s=n;for(const a of this._onBeforeChangeConversationListeners){const r=a(t,s);r!==void 0&&(s=r)}return s}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return se.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??B.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(t){this.update({rootTaskUuid:t})}get displayName(){return se.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return se.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}get flags(){return this._chatFlagModel}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var s;const t=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",n=this.hasImagesInDraft();return t||n}hasImagesInDraft(){var s;const t=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!t)return!1;const n=a=>Array.isArray(a)?a.some(n):!!a&&(a.type==="file"||!(!a.content||!Array.isArray(a.content))&&a.content.some(n));return n(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){return this.chatHistory.find(L)??null}get lastExchange(){return this.chatHistory.findLast(L)??null}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>L(t)&&t.status===E.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>Fn(t)||gt(t)||Je(t))}get totalCharactersStore(){return this._totalCharactersStore}convertHistoryToExchanges(t){if(t.length===0)return[];t=this._chatHistorySummarizationModel.preprocessChatHistory(t);const n=[];for(const s of t)if(Fn(s))n.push(Ma(s));else if(Je(s))n.push(Ma(s));else if(gt(s)&&s.fromTimestamp!==void 0&&s.toTimestamp!==void 0&&s.revertTarget){const a=pu(s,1),r={request_message:"",response_text:"",request_id:s.request_id||crypto.randomUUID(),request_nodes:[a],response_nodes:[]};n.push(r)}return n}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===E.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const n=crypto.randomUUID();let s,a="";const r=await this._addIdeStateNode(Da({...t,request_id:n,status:E.sent,timestamp:new Date().toISOString()}));for await(const o of this.sendUserMessage(n,r,!0))o.response_text&&(a+=o.response_text),o.request_id&&(s=o.request_id);return{responseText:a,requestId:s}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t.request_id,t.lastChunkId,{flags:this._chatFlagModel}))}_createStreamStateHandlers(t,n,s){return[]}_resolveUnresolvedToolUses(t,n,s){var m,d,h;if(t.length===0)return[t,n];const a=t[t.length-1],r=((m=a.response_nodes)==null?void 0:m.filter(g=>g.type===D.TOOL_USE))??[];if(r.length===0)return[t,n];const o=new Set;(d=n.structured_request_nodes)==null||d.forEach(g=>{var f;g.type===G.TOOL_RESULT&&((f=g.tool_result_node)!=null&&f.tool_use_id)&&o.add(g.tool_result_node.tool_use_id)});const i=r.filter(g=>{var b;const f=(b=g.tool_use)==null?void 0:b.tool_use_id;return f&&!o.has(f)});if(i.length===0)return[t,n];const l=i.map((g,f)=>{const b=g.tool_use.tool_use_id;return function(y,v,C,N){const P=yl(v,y,N);let S;if(P!==void 0)S=P;else{let k;switch(v.phase){case x.runnable:k="Tool was cancelled before running.";break;case x.new:k="Cancelled by user.";break;case x.checkingSafety:k="Tool was cancelled during safety check.";break;case x.running:k="Tool was cancelled while running.";break;case x.cancelling:k="Tool cancellation was interrupted.";break;case x.cancelled:k="Cancelled by user.";break;case x.error:k="Tool execution failed.";break;case x.completed:k="Tool completed but result was unavailable.";break;case x.unknown:default:k="Cancelled by user.",v.phase!==x.unknown&&console.error(`Unexpected tool state phase: ${v.phase}`)}S={tool_use_id:y,content:k,is_error:!0}}return{id:C,type:G.TOOL_RESULT,tool_result_node:S}}(b,this.getToolUseState(a.request_id,b),Wn(n.structured_request_nodes??[])+f+1,this._chatFlagModel.enableDebugFeatures)});if((h=n.structured_request_nodes)==null?void 0:h.some(g=>g.type===G.TOOL_RESULT))return[t,{...n,structured_request_nodes:[...n.structured_request_nodes??[],...l]}];{const g={request_message:"",response_text:"OK.",request_id:crypto.randomUUID(),structured_request_nodes:l,structured_output_nodes:[],status:E.success,hidden:!0};return s||this.addExchangeBeforeLast(g),[t.concat(this.convertHistoryToExchanges([g])),n]}}async*sendUserMessage(t,n,s,a){const r=await this._sendUserMessageMutex.acquire();try{yield*this._sendUserMessage(t,n,s,a)}finally{r()}}async*_sendUserMessage(t,n,s,a){var d;const r=this._specialContextInputModel.chatActiveContext;let o;if(n.chatHistory!==void 0)o=n.chatHistory;else{let h=this.successfulMessages;if(n.chatItemType===Xe.summaryTitle){const g=h.findIndex(f=>f.chatItemType!==Xe.agentOnboarding&&Yn(f));g!==-1&&(h=h.slice(g))}o=this.convertHistoryToExchanges(h)}this._chatFlagModel.enableParallelTools&&([o,n]=this._resolveUnresolvedToolUses(o,n,s));let i=this.personaType;if(n.structured_request_nodes){const h=n.structured_request_nodes.find(g=>g.type===G.CHANGE_PERSONALITY);h&&h.change_personality_node&&(i=h.change_personality_node.personality_type)}const l={text:n.request_message,chatHistory:o,silent:s,modelId:n.model_id,context:r,userSpecifiedFiles:r.userSpecifiedFiles,externalSourceIds:(d=r.externalSources)==null?void 0:d.map(h=>h.id),disableRetrieval:n.disableRetrieval??!1,disableSelectedCodeDetails:n.disableSelectedCodeDetails??!1,nodes:n.structured_request_nodes,memoriesInfo:n.memoriesInfo,personaType:i,conversationId:this.id,createdTimestamp:Date.now(),requestIdOverride:a},u=this._createStreamStateHandlers(t,l,{flags:this._chatFlagModel}),m=this._extensionClient.startChatStreamWithRetry(t,l,{flags:this._chatFlagModel});for await(const h of m){let g=h;t=h.request_id||t;for(const f of u)g=f.handleChunk(g)??g;yield g}for(const h of u)yield*h.handleComplete();this.updateExchangeById({structured_request_nodes:n.structured_request_nodes},t)}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(n=>n!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(n=>n!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(n=>n!==t)}}updateChatItem(t,n){return this.chatHistory.find(s=>s.request_id===t)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t?{...s,...n}:s)}),!0)}async _addIdeStateNode(t){let n,s=(t.structured_request_nodes??[]).filter(a=>a.type!==G.IDE_STATE);try{n=await this._extensionClient.getChatRequestIdeState()}catch(a){console.error("Failed to add IDE state to exchange:",a)}return n?(s=[...s,{id:Wn(s)+1,type:G.IDE_STATE,ide_state_node:n}],{...t,structured_request_nodes:s}):t}}function pu(e,t){const n=(gt(e),e.fromTimestamp),s=(gt(e),e.toTimestamp),a=gt(e)&&e.revertTarget!==void 0;return{id:t,type:G.CHECKPOINT_REF,checkpoint_ref_node:{request_id:e.request_id||"",from_timestamp:n,to_timestamp:s,source:a?lo.CHECKPOINT_REVERT:void 0}}}function Ma(e){const t=(e.structured_output_nodes??[]).filter(n=>n.type===D.RAW_RESPONSE||n.type===D.TOOL_USE||n.type===D.TOOL_USE_START).map(n=>n.type===D.TOOL_USE_START?{...n,tool_use:{...n.tool_use,input_json:"{}"},type:D.TOOL_USE}:n);return{request_message:e.request_message,response_text:e.response_text??"",request_id:e.request_id||"",request_nodes:e.structured_request_nodes??[],response_nodes:t}}function Wn(e){return e.length>0?Math.max(...e.map(t=>t.id)):0}function Da(e){var t;if(e.request_message.length>0&&!((t=e.structured_request_nodes)!=null&&t.some(n=>n.type===G.TEXT))){let n=e.structured_request_nodes??[];return n=[...n,{id:Wn(n)+1,type:G.TEXT,text_node:{content:e.request_message}}],{...e,structured_request_nodes:n}}return e}const Tc="augment-welcome";var E=(e=>(e.draft="draft",e.sent="sent",e.failed="failed",e.success="success",e.cancelled="cancelled",e))(E||{}),$e=(e=>(e.running="running",e.awaitingUserAction="awaiting-user-action",e.notRunning="not-running",e))($e||{}),z=(e=>(e.seen="seen",e.unseen="unseen",e))(z||{}),Xe=(e=>(e.signInWelcome="sign-in-welcome",e.generateCommitMessage="generate-commit-message",e.summaryResponse="summary-response",e.summaryTitle="summary-title",e.educateFeatures="educate-features",e.agentOnboarding="agent-onboarding",e.agenticTurnDelimiter="agentic-turn-delimiter",e.agenticRevertDelimiter="agentic-revert-delimiter",e.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",e.exchange="exchange",e.exchangePointer="exchange-pointer",e.historySummary="history-summary",e))(Xe||{});function Fa(e){return L(e)||fu(e)||yu(e)}function L(e){return!!e&&(e.chatItemType===void 0||e.chatItemType==="agent-onboarding")}function Fn(e){return L(e)&&e.status==="success"}function gu(e){return!!e&&e.chatItemType==="exchange-pointer"}function Ec(e){return e.chatItemType==="sign-in-welcome"}function fu(e){return e.chatItemType==="generate-commit-message"}function Sc(e){return e.chatItemType==="summary-response"}function Ic(e){return e.chatItemType==="educate-features"}function yu(e){return e.chatItemType==="agent-onboarding"}function Nc(e){return e.chatItemType==="agentic-turn-delimiter"}function gt(e){return e.chatItemType==="agentic-checkpoint-delimiter"}function Je(e){return e.chatItemType==="history-summary"}function wc(e){return e.revertTarget!==void 0}function Cc(e,t){const n=function(a){if(!a)return;const r=a.findLast(o=>Fa(o.turn));return r?r.turn:void 0}(e);if(!((n==null?void 0:n.status)==="success"||(n==null?void 0:n.status)==="failed"||(n==null?void 0:n.status)==="cancelled"))return!1;const s=function(a){return a?a.findLast(o=>{var i;return!((i=o.turn.request_id)!=null&&i.startsWith(Gt))&&Fa(o.turn)}):void 0}(e);return(s==null?void 0:s.turn.request_id)===t.request_id}function kc(e){var t;return((t=e.structured_output_nodes)==null?void 0:t.some(n=>n.type===D.TOOL_USE))??!1}function Rc(e){var t;return((t=e.structured_request_nodes)==null?void 0:t.some(n=>n.type===G.TOOL_RESULT))??!1}function xc(e){return!(!e||typeof e!="object")&&(!("request_id"in e)||typeof e.request_id=="string")&&(!("seen_state"in e)||e.seen_state==="seen"||e.seen_state==="unseen")}function Ac(e){return(e==null?void 0:e.status)==="success"||(e==null?void 0:e.status)==="failed"||(e==null?void 0:e.status)==="cancelled"}function Oc(e){if(!e)return;const t=e.filter(n=>L(n.turn)).map(n=>{return"response_text"in(s=n.turn)?s.response_text??"":"";var s}).filter(n=>n.length>0);return t.length>0?t.join(`
`):void 0}async function*bu(e,t=1e3){for(;e>0;)yield e,await new Promise(n=>setTimeout(n,Math.min(t,e))),e-=t}class _u{constructor(t,n,s,a=5,r=4e3,o){c(this,"_isCancelled",!1);this.requestId=t,this.chatMessage=n,this.startStreamFn=s,this.maxRetries=a,this.baseDelay=r,this.flags=o}cancel(){this._isCancelled=!0}async*getStream(){let t=0,n=0,s=!1;try{for(;!this._isCancelled;){const a=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let r,o,i=!1,l=!0;for await(const u of a){if(u.status===E.failed){if(u.isRetriable!==!0||s)return yield u;i=!0,l=u.shouldBackoff??!0,r=u.display_error_message,o=u.request_id;break}s=!0,yield u}if(!i)return;if(this._isCancelled)return yield this.createCancelledStatus();if(t++,t>this.maxRetries)return console.error(`Failed after ${this.maxRetries} attempts: ${r}`),void(yield{request_id:o??this.requestId,seen_state:z.unseen,status:E.failed,display_error_message:r,isRetriable:!1});if(l){const u=this.baseDelay*2**n;n++;for await(const m of bu(u))yield{request_id:this.requestId,status:E.sent,display_error_message:`Service temporarily unavailable. Retrying in ${Math.floor(m/1e3)} seconds... (Attempt ${t} of ${this.maxRetries})`,isRetriable:!0}}yield{request_id:this.requestId,status:E.sent,display_error_message:`Generating response... (Attempt ${t+1})`,isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(a){console.error("Unexpected error in chat stream:",a),yield{request_id:this.requestId,seen_state:z.unseen,status:E.failed,display_error_message:a instanceof Error?a.message:String(a)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:z.unseen,status:E.cancelled}}}var ze=(e=>(e.getHydratedTaskRequest="get-hydrated-task-request",e.getHydratedTaskResponse="get-hydrated-task-response",e.setCurrentRootTaskUuid="set-current-root-task-uuid",e.createTaskRequest="create-task-request",e.createTaskResponse="create-task-response",e.updateTaskRequest="update-task-request",e.updateTaskResponse="update-task-response",e.updateHydratedTaskRequest="update-hydrated-task-request",e.updateHydratedTaskResponse="update-hydrated-task-response",e))(ze||{});class vu{constructor(t){c(this,"getHydratedTask",async t=>{const n={type:ze.getHydratedTaskRequest,data:{uuid:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.task});c(this,"createTask",async(t,n,s)=>{const a={type:ze.createTaskRequest,data:{name:t,description:n,parentTaskUuid:s}};return(await this._asyncMsgSender.sendToSidecar(a,3e4)).data.uuid});c(this,"updateTask",async(t,n,s)=>{const a={type:ze.updateTaskRequest,data:{uuid:t,updates:n,updatedBy:s}};await this._asyncMsgSender.sendToSidecar(a,3e4)});c(this,"setCurrentRootTaskUuid",t=>{const n={type:ze.setCurrentRootTaskUuid,data:{uuid:t}};this._asyncMsgSender.sendToSidecar(n)});c(this,"updateHydratedTask",async(t,n)=>{const s={type:ze.updateHydratedTaskRequest,data:{task:t,updatedBy:n}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});this._asyncMsgSender=t}}var to=(e=>(e.getRulesListRequest="get-rules-list-request",e.getRulesListResponse="get-rules-list-response",e.createRule="create-rule",e.createRuleResponse="create-rule-response",e.openRule="open-rule",e.openGuidelines="open-guidelines",e.deleteRule="delete-rule",e.updateRuleFile="update-rule-file",e.updateRuleFileResponse="update-rule-file-response",e.getWorkspaceRoot="get-workspace-root",e.getWorkspaceRootResponse="get-workspace-root-response",e.autoImportRules="auto-import-rules",e.autoImportRulesOptionsResponse="auto-import-rules-options-response",e.autoImportRulesSelectionRequest="auto-import-rules-selection-request",e.autoImportRulesResponse="auto-import-rules-response",e.processSelectedPathsRequest="process-selected-paths-request",e.processSelectedPathsResponse="process-selected-paths-response",e))(to||{}),Vt=(e=>(e.loadConversationToolUseStatesRequest="load-conversation-tooluse-states-request",e.loadConversationToolUseStatesResponse="load-conversation-tooluse-states-response",e.saveToolUseStatesRequest="save-tooluse-states-request",e.saveToolUseStatesResponse="save-tooluse-states-response",e.deleteConversationToolUseStatesRequest="delete-conversation-tooluse-states-request",e.deleteConversationToolUseStatesResponse="delete-conversation-tooluse-states-response",e))(Vt||{});class Mc{constructor(t,n,s){c(this,"_taskClient");c(this,"getChatInitData",async()=>{const t=await this._asyncMsgSender.send({type:_.chatLoaded},3e4);if(t.data.enableDebugFeatures)try{console.log("Running hello world test...");const n=await async function(s){return(await Ri(Ai,new Or({sendMessage:r=>{s.postMessage(r)},onReceiveMessage:r=>{const o=i=>{r(i.data)};return window.addEventListener("message",o),()=>{window.removeEventListener("message",o)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host);console.log("Hello world result:",n)}catch(n){console.error("Hello world error:",n)}return t.data});c(this,"reportWebviewClientEvent",t=>{this._asyncMsgSender.send({type:_.reportWebviewClientMetric,data:{webviewName:Dr.chat,client_metric:t,value:1}})});c(this,"trackEventWithTypes",(t,n)=>{this._asyncMsgSender.send({type:_.trackAnalyticsEvent,data:{eventName:t,properties:n}})});c(this,"reportAgentSessionEvent",t=>{this._asyncMsgSender.sendToSidecar({type:q.reportAgentSessionEvent,data:t})});c(this,"reportAgentRequestEvent",t=>{this._asyncMsgSender.sendToSidecar({type:q.reportAgentRequestEvent,data:t})});c(this,"getSuggestions",async(t,n=!1)=>{const s={rootPath:"",relPath:t},a=this.findFiles(s,6),r=this.findRecentlyOpenedFiles(s,6),o=this.findFolders(s,3),i=this.findExternalSources(t,n),l=this._flags.enableRules?this.findRules(t,6):Promise.resolve([]),[u,m,d,h,g]=await Promise.all([lt(a,[]),lt(r,[]),lt(o,[]),lt(i,[]),lt(l,[])]),f=(y,v)=>({...cl(y),[v]:y}),b=[...u.map(y=>f(y,"file")),...d.map(y=>f(y,"folder")),...m.map(y=>f(y,"recentFile")),...h.map(y=>({label:y.name,name:y.name,id:y.id,externalSource:y})),...g.map(y=>({...dl(y),rule:y}))];if(this._flags.enablePersonalities){const y=this.getPersonalities(t);y.length>0&&b.push(...y)}return b});c(this,"getPersonalities",t=>{if(!this._flags.enablePersonalities)return[];if(t==="")return ya;const n=t.toLowerCase();return ya.filter(s=>{const a=s.personality.description.toLowerCase(),r=s.label.toLowerCase();return a.includes(n)||r.includes(n)})});c(this,"sendAction",t=>{this._host.postMessage({type:_.mainPanelPerformAction,data:t})});c(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:_.showAugmentPanel})});c(this,"showNotification",t=>{this._host.postMessage({type:_.showNotification,data:t})});c(this,"openConfirmationModal",async t=>(await this._asyncMsgSender.send({type:_.openConfirmationModal,data:t},1e9)).data.ok);c(this,"clearMetadataFor",t=>{this._host.postMessage({type:_.chatClearMetadata,data:t})});c(this,"resolvePath",async(t,n=void 0)=>{const s=await this._asyncMsgSender.send({type:_.resolveFileRequest,data:{...t,exactMatch:!0,maxResults:1,searchScope:n}},5e3);if(s.data)return s.data});c(this,"resolveSymbols",async(t,n)=>(await this._asyncMsgSender.send({type:_.findSymbolRequest,data:{query:t,searchScope:n}},3e4)).data);c(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:_.getDiagnosticsRequest},1e3)).data);c(this,"findFiles",async(t,n=12)=>(await this._asyncMsgSender.send({type:_.findFileRequest,data:{...t,maxResults:n}},5e3)).data);c(this,"findFolders",async(t,n=12)=>(await this._asyncMsgSender.send({type:_.findFolderRequest,data:{...t,maxResults:n}},5e3)).data);c(this,"findRecentlyOpenedFiles",async(t,n=12)=>(await this._asyncMsgSender.send({type:_.findRecentlyOpenedFilesRequest,data:{...t,maxResults:n}},5e3)).data);c(this,"findExternalSources",async(t,n=!1)=>this._flags.enableExternalSourcesInChat?n?[]:(await this._asyncMsgSender.send({type:_.findExternalSourcesRequest,data:{query:t,source_types:[]}},5e3)).data.sources??[]:[]);c(this,"findRules",async(t,n=12)=>(await this._asyncMsgSender.sendToSidecar({type:to.getRulesListRequest,data:{query:t,maxResults:n}})).data.rules);c(this,"openFile",t=>{this._host.postMessage({type:_.openFile,data:t})});c(this,"saveFile",t=>this._host.postMessage({type:_.saveFile,data:t}));c(this,"loadFile",t=>this._host.postMessage({type:_.loadFile,data:t}));c(this,"openMemoriesFile",()=>{this._host.postMessage({type:_.openMemoriesFile})});c(this,"canShowTerminal",async(t,n)=>{try{return(await this._asyncMsgSender.send({type:_.canShowTerminal,data:{terminalId:t,command:n}},5e3)).data.canShow}catch(s){return console.error("Failed to check if terminal can be shown:",s),!1}});c(this,"showTerminal",async(t,n)=>{try{return(await this._asyncMsgSender.send({type:_.showTerminal,data:{terminalId:t,command:n}},5e3)).data.success}catch(s){return console.error("Failed to show terminal:",s),!1}});c(this,"createFile",(t,n)=>{this._host.postMessage({type:_.chatCreateFile,data:{code:t,relPath:n}})});c(this,"openScratchFile",async(t,n="shellscript")=>{await this._asyncMsgSender.send({type:_.openScratchFileRequest,data:{content:t,language:n}},1e4)});c(this,"resolveWorkspaceFileChunk",async t=>{try{return(await this._asyncMsgSender.send({type:_.resolveWorkspaceFileChunkRequest,data:t},5e3)).data}catch{return}});c(this,"smartPaste",t=>{this._host.postMessage({type:_.chatSmartPaste,data:t})});c(this,"getHydratedTask",async t=>this._taskClient.getHydratedTask(t));c(this,"updateHydratedTask",async(t,n)=>this._taskClient.updateHydratedTask(t,n));c(this,"setCurrentRootTaskUuid",t=>{this._taskClient.setCurrentRootTaskUuid(t)});c(this,"createTask",async(t,n,s)=>this._taskClient.createTask(t,n,s));c(this,"updateTask",async(t,n,s)=>this._taskClient.updateTask(t,n,s));c(this,"saveChat",async(t,n,s)=>this._asyncMsgSender.send({type:_.saveChat,data:{conversationId:t,chatHistory:n,title:s}},5e3));c(this,"updateUserGuidelines",t=>{this._host.postMessage({type:_.updateUserGuidelines,data:t})});c(this,"updateWorkspaceGuidelines",t=>{this._host.postMessage({type:_.updateWorkspaceGuidelines,data:t})});c(this,"openSettingsPage",t=>{this._host.postMessage({type:_.openSettingsPage,data:t})});c(this,"_activeRetryStreams",new Map);c(this,"cancelChatStream",async t=>{var n;(n=this._activeRetryStreams.get(t))==null||n.cancel(),await this._asyncMsgSender.send({type:_.chatUserCancel,data:{requestId:t}},1e4)});c(this,"sendUserRating",async(t,n,s,a="")=>{const r={requestId:t,rating:s,note:a,mode:n},o={type:_.chatRating,data:r};return(await this._asyncMsgSender.send(o,3e4)).data});c(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:_.usedChat})});c(this,"createProject",t=>{this._host.postMessage({type:_.mainPanelCreateProject,data:{name:t}})});c(this,"openProjectFolder",()=>{this._host.postMessage({type:_.mainPanelPerformAction,data:"open-folder"})});c(this,"closeProjectFolder",()=>{this._host.postMessage({type:_.mainPanelPerformAction,data:"close-folder"})});c(this,"cloneRepository",()=>{this._host.postMessage({type:_.mainPanelPerformAction,data:"clone-repository"})});c(this,"grantSyncPermission",()=>{this._host.postMessage({type:_.mainPanelPerformAction,data:"grant-sync-permission"})});c(this,"startRemoteMCPAuth",t=>{this._host.postMessage({type:_.startRemoteMCPAuth,data:{name:t}})});c(this,"callTool",async(t,n,s,a,r,o)=>{const i={type:_.callTool,data:{chatRequestId:t,toolUseId:n,name:s,input:a,chatHistory:r,conversationId:o}};return(await this._asyncMsgSender.send(i,0)).data});c(this,"cancelToolRun",async(t,n)=>{const s={type:_.cancelToolRun,data:{requestId:t,toolUseId:n}};await this._asyncMsgSender.send(s,0)});c(this,"checkSafe",async t=>{const n={type:qt.checkToolCallSafeRequest,data:t};return(await this._asyncMsgSender.sendToSidecar(n,0)).data});c(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:qt.closeAllToolProcesses},0)});c(this,"getToolIdentifier",async t=>{const n={type:qt.getToolIdentifierRequest,data:{toolName:t}};return(await this._asyncMsgSender.sendToSidecar(n,0)).data});c(this,"getChatMode",async()=>{const t={type:q.getChatModeRequest};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.chatMode});c(this,"setChatMode",t=>{this._asyncMsgSender.send({type:_.chatModeChanged,data:{mode:t}})});c(this,"getAgentEditList",async(t,n)=>{const s={type:q.getEditListRequest,data:{fromTimestamp:t,toTimestamp:n}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});c(this,"hasChangesSince",async t=>{const n={type:q.getEditListRequest,data:{fromTimestamp:t,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.edits.filter(s=>{var a,r;return((a=s.changesSummary)==null?void 0:a.totalAddedLines)||((r=s.changesSummary)==null?void 0:r.totalRemovedLines)}).length>0});c(this,"getToolCallCheckpoint",async t=>{const n={type:_.getToolCallCheckpoint,data:{requestId:t}};return(await this._asyncMsgSender.send(n,3e4)).data.checkpointNumber});c(this,"setCurrentConversation",t=>{this._asyncMsgSender.sendToSidecar({type:q.setCurrentConversation,data:{conversationId:t}})});c(this,"migrateConversationId",async(t,n)=>{await this._asyncMsgSender.sendToSidecar({type:q.migrateConversationId,data:{oldConversationId:t,newConversationId:n}},3e4)});c(this,"showAgentReview",(t,n,s,a=!0,r)=>{this._asyncMsgSender.sendToSidecar({type:q.chatReviewAgentFile,data:{qualifiedPathName:t,fromTimestamp:n,toTimestamp:s,retainFocus:a,useNativeDiffIfAvailable:r}})});c(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:q.chatAgentEditAcceptAll}),!0));c(this,"revertToTimestamp",async(t,n)=>(await this._asyncMsgSender.sendToSidecar({type:q.revertToTimestamp,data:{timestamp:t,qualifiedPathNames:n}}),!0));c(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:_.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);c(this,"getAgentEditChangesByRequestId",async t=>{const n={type:q.getEditChangesByRequestIdRequest,data:{requestId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});c(this,"getAgentEditContentsByRequestId",async t=>{const n={type:q.getAgentEditContentsByRequestId,data:{requestId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});c(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:_.triggerInitialOrientation})});c(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:_.getWorkspaceInfoRequest},5e3)).data}catch(t){return console.error("Error getting workspace info:",t),{}}});c(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:_.toggleCollapseUnchangedRegions})});c(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:_.checkAgentAutoModeApproval},5e3)).data);c(this,"setAgentAutoModeApproved",async t=>{await this._asyncMsgSender.send({type:_.setAgentAutoModeApproved,data:t},5e3)});c(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:q.checkHasEverUsedAgent},5e3)).data);c(this,"setHasEverUsedAgent",async t=>{await this._asyncMsgSender.sendToSidecar({type:q.setHasEverUsedAgent,data:t},5e3)});c(this,"checkHasEverUsedRemoteAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:q.checkHasEverUsedRemoteAgent},5e3)).data);c(this,"setHasEverUsedRemoteAgent",async t=>{await this._asyncMsgSender.sendToSidecar({type:q.setHasEverUsedRemoteAgent,data:t},5e3)});c(this,"getChatRequestIdeState",async()=>{const t={type:_.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(t,3e4)).data});c(this,"reportError",t=>{this._host.postMessage({type:_.reportError,data:t})});c(this,"sendMemoryCreated",async t=>{await this._asyncMsgSender.sendToSidecar(t,5e3)});c(this,"sendGitMessage",async t=>await this._asyncMsgSender.sendToSidecar(t,3e4));this._host=t,this._asyncMsgSender=n,this._flags=s,this._taskClient=new vu(n)}async*generateCommitMessage(){const t={type:_.generateCommitMessage},n=this._asyncMsgSender.stream(t,3e4,6e4);yield*Un(n,()=>{},this._flags.retryChatStreamTimeouts)}async*sendInstructionMessage(t,n){const s={instruction:t.request_message??"",selectedCodeDetails:n,requestId:t.request_id},a={type:_.chatInstructionMessage,data:s},r=this._asyncMsgSender.stream(a,3e4,6e4);yield*async function*(o){let i;try{for await(const l of o)i=l.data.requestId,yield{request_id:i,response_text:l.data.text,seen_state:z.unseen,status:E.sent};yield{request_id:i,seen_state:z.unseen,status:E.success}}catch(l){console.error("Error in chat instruction model reply stream:",l),yield{request_id:i,seen_state:z.unseen,status:E.failed}}}(r)}async openGuidelines(t){this._host.postMessage({type:_.openGuidelines,data:t})}async*getExistingChatStream(t,n,s){const a=s==null?void 0:s.flags.enablePreferenceCollection,r=a?1e9:6e4,o=a?1e9:3e5,i={type:_.chatGetStreamRequest,data:{requestId:t,lastChunkId:n}},l=this._asyncMsgSender.stream(i,r,o);yield*Un(l,this.reportError,this._flags.retryChatStreamTimeouts)}async*startChatStream(t,n){const s=n==null?void 0:n.flags.enablePreferenceCollection,a=s?1e9:1e5,r=s?1e9:3e5,o={type:_.chatUserMessage,data:t},i=this._asyncMsgSender.stream(o,a,r);yield*Un(i,this.reportError,this._flags.retryChatStreamTimeouts)}async checkToolExists(t){return(await this._asyncMsgSender.send({type:_.checkToolExists,toolName:t},0)).exists}async saveImage(t,n){const s=ga(await In(t)),a=n??`${await pa(await Nn(s))}.${t.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:_.chatSaveImageRequest,data:{filename:a,data:s}},1e4)).data}async saveAttachment(t,n){const s=ga(await In(t)),a=n??`${await pa(await Nn(s))}.${t.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:_.chatSaveAttachmentRequest,data:{filename:a,data:s}},1e4)).data}async loadImage(t){const n=await this._asyncMsgSender.send({type:_.chatLoadImageRequest,data:t},1e4),s=n.data?await Nn(n.data):void 0;if(!s)return;let a="application/octet-stream";const r=t.split(".").at(-1);r==="png"?a="image/png":r!=="jpg"&&r!=="jpeg"||(a="image/jpeg");const o=new File([s],t,{type:a});return await In(o)}async deleteImage(t){await this._asyncMsgSender.send({type:_.chatDeleteImageRequest,data:t},1e4)}async*startChatStreamWithRetry(t,n,s){const a=new _u(t,n,(r,o)=>this.startChatStream(r,o),(s==null?void 0:s.maxRetries)??5,4e3,s==null?void 0:s.flags);this._activeRetryStreams.set(t,a);try{yield*a.getStream()}finally{this._activeRetryStreams.delete(t)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:_.getSubscriptionInfo},5e3)}async loadExchanges(t,n){if(n.length===0)return[];const s={type:Ht.loadExchangesByUuidsRequest,data:{conversationId:t,uuids:n}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data.exchanges}async saveExchanges(t,n){if(n.length===0)return;const s={type:Ht.saveExchangesRequest,data:{conversationId:t,exchanges:n}};await this._asyncMsgSender.sendToSidecar(s,3e4)}async deleteConversationExchanges(t){const n={type:Ht.deleteConversationExchangesRequest,data:{conversationId:t}};await this._asyncMsgSender.sendToSidecar(n,3e4)}async loadConversationToolUseStates(t){const n={type:Vt.loadConversationToolUseStatesRequest,data:{conversationId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.toolUseStates}async saveToolUseStates(t,n){if(Object.keys(n).length===0)return;const s={type:Vt.saveToolUseStatesRequest,data:{conversationId:t,toolUseStates:n}};await this._asyncMsgSender.sendToSidecar(s,3e4)}async deleteConversationToolUseStates(t){const n={type:Vt.deleteConversationToolUseStatesRequest,data:{conversationId:t}};await this._asyncMsgSender.sendToSidecar(n,3e4)}}async function*Un(e,t=()=>{},n){let s;try{for await(const a of e){if(s=a.data.requestId,a.data.error)return console.error("Error in chat model reply stream:",a.data.error.displayErrorMessage),yield{request_id:s,seen_state:z.unseen,status:E.failed,display_error_message:a.data.error.displayErrorMessage,isRetriable:a.data.error.isRetriable,shouldBackoff:a.data.error.shouldBackoff};const r={request_id:s,response_text:a.data.text,workspace_file_chunks:a.data.workspaceFileChunks,structured_output_nodes:Tu(a.data.nodes),seen_state:z.unseen,status:E.sent,lastChunkId:a.data.chunkId};a.data.stop_reason!=null&&(r.stop_reason=a.data.stop_reason),yield r}yield{request_id:s,seen_state:z.unseen,status:E.success}}catch(a){let r,o;if(t({originalRequestId:s||"",sanitizedMessage:a instanceof Error?a.message:String(a),stackTrace:a instanceof Error&&a.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),a instanceof ao&&n)switch(a.name){case"MessageTimeout":r=!0,o=!1;break;case"StreamTimeout":case"InvalidResponse":r=!1}console.error("Unexpected error in chat model reply stream:",a),yield{request_id:s,seen_state:z.unseen,status:E.failed,isRetriable:r,shouldBackoff:o}}}async function lt(e,t){try{return await e}catch(n){return console.warn(`Error while resolving promise: ${n}`),t}}function Tu(e){if(!e)return e;let t=!1;return e.filter(n=>n.type!==D.TOOL_USE||!t&&(t=!0,!0))}const Dc=15,Fc=1e3,Eu=25e4,Uc=2e4;class Pc{constructor(t){c(this,"_enableEditableHistory",!1);c(this,"_enablePreferenceCollection",!1);c(this,"_enableRetrievalDataCollection",!1);c(this,"_enableDebugFeatures",!1);c(this,"_enableConversationDebugUtils",!1);c(this,"_enableRichTextHistory",!1);c(this,"_enableAgentSwarmMode",!1);c(this,"_modelDisplayNameToId",{});c(this,"_fullFeatured",!0);c(this,"_enableExternalSourcesInChat",!1);c(this,"_smallSyncThreshold",15);c(this,"_bigSyncThreshold",1e3);c(this,"_enableSmartPaste",!1);c(this,"_enableDirectApply",!1);c(this,"_summaryTitles",!1);c(this,"_suggestedEditsAvailable",!1);c(this,"_enableShareService",!1);c(this,"_maxTrackableFileCount",Eu);c(this,"_enableDesignSystemRichTextEditor",!1);c(this,"_enableSources",!1);c(this,"_enableChatMermaidDiagrams",!1);c(this,"_smartPastePrecomputeMode",ro.visibleHover);c(this,"_useNewThreadsMenu",!1);c(this,"_enableChatMermaidDiagramsMinVersion",!1);c(this,"_enablePromptEnhancer",!1);c(this,"_idleNewSessionNotificationTimeoutMs");c(this,"_idleNewSessionMessageTimeoutMs");c(this,"_enableChatMultimodal",!1);c(this,"_enableAgentMode",!1);c(this,"_enableAgentAutoMode",!1);c(this,"_enableRichCheckpointInfo",!1);c(this,"_agentMemoriesFilePathName");c(this,"_conversationHistorySizeThresholdBytes",44040192);c(this,"_userTier","unknown");c(this,"_eloModelConfiguration",{highPriorityModels:[],regularBattleModels:[],highPriorityThreshold:.5});c(this,"_truncateChatHistory",!1);c(this,"_enableBackgroundAgents",!1);c(this,"_enableNewThreadsList",!1);c(this,"_customPersonalityPrompts",{});c(this,"_enablePersonalities",!1);c(this,"_enableRules",!1);c(this,"_memoryClassificationOnFirstToken",!1);c(this,"_enableGenerateCommitMessage",!1);c(this,"_modelRegistry",{});c(this,"_enableModelRegistry",!1);c(this,"_enableTaskList",!1);c(this,"_clientAnnouncement","");c(this,"_useHistorySummary",!1);c(this,"_historySummaryParams","");c(this,"_enableExchangeStorage",!1);c(this,"_enableToolUseStateStorage",!1);c(this,"_retryChatStreamTimeouts",!1);c(this,"_enableCommitIndexing",!1);c(this,"_enableMemoryRetrieval",!1);c(this,"_enableAgentTabs",!1);c(this,"_isVscodeVersionOutdated",!1);c(this,"_vscodeMinVersion","");c(this,"_enableGroupedTools",!1);c(this,"_remoteAgentsResumeHintAvailableTtlDays",0);c(this,"_enableParallelTools",!1);c(this,"_enableAgentGitTracker",!1);c(this,"_memoriesParams",{});c(this,"_subscribers",new Set);c(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));c(this,"update",t=>{this._enableEditableHistory=t.enableEditableHistory??this._enableEditableHistory,this._enablePreferenceCollection=t.enablePreferenceCollection??this._enablePreferenceCollection,this._enableRetrievalDataCollection=t.enableRetrievalDataCollection??this._enableRetrievalDataCollection,this._enableDebugFeatures=t.enableDebugFeatures??this._enableDebugFeatures,this._enableConversationDebugUtils=t.enableConversationDebugUtils??this._enableConversationDebugUtils,this._enableRichTextHistory=t.enableRichTextHistory??this._enableRichTextHistory,this._enableAgentSwarmMode=t.enableAgentSwarmMode??this._enableAgentSwarmMode,this._modelDisplayNameToId={...t.modelDisplayNameToId},this._fullFeatured=t.fullFeatured??this._fullFeatured,this._enableExternalSourcesInChat=t.enableExternalSourcesInChat??this._enableExternalSourcesInChat,this._smallSyncThreshold=t.smallSyncThreshold??this._smallSyncThreshold,this._bigSyncThreshold=t.bigSyncThreshold??this._bigSyncThreshold,this._enableSmartPaste=t.enableSmartPaste??this._enableSmartPaste,this._enableDirectApply=t.enableDirectApply??this._enableDirectApply,this._summaryTitles=t.summaryTitles??this._summaryTitles,this._suggestedEditsAvailable=t.suggestedEditsAvailable??this._suggestedEditsAvailable,this._enableShareService=t.enableShareService??this._enableShareService,this._maxTrackableFileCount=t.maxTrackableFileCount??this._maxTrackableFileCount,this._enableDesignSystemRichTextEditor=t.enableDesignSystemRichTextEditor??this._enableDesignSystemRichTextEditor,this._enableSources=t.enableSources??this._enableSources,this._enableChatMermaidDiagrams=t.enableChatMermaidDiagrams??this._enableChatMermaidDiagrams,this._smartPastePrecomputeMode=t.smartPastePrecomputeMode??this._smartPastePrecomputeMode,this._useNewThreadsMenu=t.useNewThreadsMenu??this._useNewThreadsMenu,this._enableChatMermaidDiagramsMinVersion=t.enableChatMermaidDiagramsMinVersion??this._enableChatMermaidDiagramsMinVersion,this._enablePromptEnhancer=t.enablePromptEnhancer??this._enablePromptEnhancer,this._idleNewSessionMessageTimeoutMs=t.idleNewSessionMessageTimeoutMs??(t.enableDebugFeatures?this._idleNewSessionMessageTimeoutMs??3e5:this._idleNewSessionMessageTimeoutMs),this._idleNewSessionNotificationTimeoutMs=t.idleNewSessionNotificationTimeoutMs??0,this._enableChatMultimodal=t.enableChatMultimodal??this._enableChatMultimodal,this._enableAgentMode=t.enableAgentMode??this._enableAgentMode,this._enableAgentAutoMode=t.enableAgentAutoMode??this._enableAgentAutoMode,this._enableRichCheckpointInfo=t.enableRichCheckpointInfo??this._enableRichCheckpointInfo,this._agentMemoriesFilePathName=t.agentMemoriesFilePathName??this._agentMemoriesFilePathName,this._conversationHistorySizeThresholdBytes=t.conversationHistorySizeThresholdBytes??this._conversationHistorySizeThresholdBytes,this._userTier=t.userTier??this._userTier,this._eloModelConfiguration=t.eloModelConfiguration??this._eloModelConfiguration,this._truncateChatHistory=t.truncateChatHistory??this._truncateChatHistory,this._enableBackgroundAgents=t.enableBackgroundAgents??this._enableBackgroundAgents,this._enableNewThreadsList=t.enableNewThreadsList??this._enableNewThreadsList,this._customPersonalityPrompts=t.customPersonalityPrompts??this._customPersonalityPrompts,this._enablePersonalities=t.enablePersonalities??this._enablePersonalities,this._enableRules=t.enableRules??this._enableRules,this._memoryClassificationOnFirstToken=t.memoryClassificationOnFirstToken??this._memoryClassificationOnFirstToken,this._enableGenerateCommitMessage=t.enableGenerateCommitMessage??this._enableGenerateCommitMessage,this._modelRegistry=t.modelRegistry??this._modelRegistry,this._enableModelRegistry=t.enableModelRegistry??this._enableModelRegistry,this._enableTaskList=t.enableTaskList??this._enableTaskList,this._clientAnnouncement=t.clientAnnouncement??this._clientAnnouncement,this._useHistorySummary=t.useHistorySummary??this._useHistorySummary,this._historySummaryParams=t.historySummaryParams??this._historySummaryParams,this._enableExchangeStorage=t.enableExchangeStorage??this._enableExchangeStorage,this._retryChatStreamTimeouts=t.retryChatStreamTimeouts??this._retryChatStreamTimeouts,this._enableCommitIndexing=t.enableCommitIndexing??this._enableCommitIndexing,this._enableMemoryRetrieval=t.enableMemoryRetrieval??this._enableMemoryRetrieval,this._enableAgentTabs=t.enableAgentTabs??this._enableAgentTabs,this._isVscodeVersionOutdated=t.isVscodeVersionOutdated??this._isVscodeVersionOutdated,this._vscodeMinVersion=t.vscodeMinVersion??this._vscodeMinVersion,this._enableGroupedTools=t.enableGroupedTools??this._enableGroupedTools,this._remoteAgentsResumeHintAvailableTtlDays=t.remoteAgentsResumeHintAvailableTtlDays??this._remoteAgentsResumeHintAvailableTtlDays,this._enableToolUseStateStorage=t.enableToolUseStateStorage??this._enableToolUseStateStorage,this._enableParallelTools=t.enableParallelTools??this._enableParallelTools,this._enableAgentGitTracker=t.enableAgentGitTracker??this._enableAgentGitTracker,this._memoriesParams=t.memoriesParams??this._memoriesParams,this._subscribers.forEach(n=>n(this))});c(this,"isModelIdValid",t=>t!==void 0&&(Object.values(this._modelDisplayNameToId).includes(t)||Object.values(this._modelRegistry).includes(t??"")));c(this,"getModelDisplayName",t=>{if(t!==void 0)return Object.keys(this._modelDisplayNameToId).find(n=>this._modelDisplayNameToId[n]===t)});t&&this.update(t)}get enableEditableHistory(){return this._fullFeatured&&(this._enableEditableHistory||this._enableDebugFeatures)}get enablePreferenceCollection(){return this._enablePreferenceCollection}get enableRetrievalDataCollection(){return this._enableRetrievalDataCollection}get enableDebugFeatures(){return this._enableDebugFeatures}get enableConversationDebugUtils(){return this._enableConversationDebugUtils||this._enableDebugFeatures}get enableGenerateCommitMessage(){return this._enableGenerateCommitMessage}get enableRichTextHistory(){return this._enableRichTextHistory||this._enableDebugFeatures}get enableAgentSwarmMode(){return this._enableAgentSwarmMode}get modelDisplayNameToId(){return this._modelDisplayNameToId}get orderedModelDisplayNames(){return Object.keys(this._modelDisplayNameToId).sort((t,n)=>{const s=t.toLowerCase(),a=n.toLowerCase();return s==="default"&&a!=="default"?-1:a==="default"&&s!=="default"?1:t.localeCompare(n)})}get fullFeatured(){return this._fullFeatured}get enableExternalSourcesInChat(){return this._enableExternalSourcesInChat}get smallSyncThreshold(){return this._smallSyncThreshold}get bigSyncThreshold(){return this._bigSyncThreshold}get enableSmartPaste(){return this._enableDebugFeatures||this._enableSmartPaste}get enableDirectApply(){return this._enableDirectApply||this._enableDebugFeatures}get enableShareService(){return this._enableShareService}get summaryTitles(){return this._summaryTitles}get suggestedEditsAvailable(){return this._suggestedEditsAvailable}get maxTrackableFileCount(){return this._maxTrackableFileCount}get enableSources(){return this._enableDebugFeatures||this._enableSources}get enableChatMermaidDiagrams(){return this._enableDebugFeatures||this._enableChatMermaidDiagrams}get smartPastePrecomputeMode(){return this._smartPastePrecomputeMode}get useNewThreadsMenu(){return this._useNewThreadsMenu}get enableChatMermaidDiagramsMinVersion(){return this._enableChatMermaidDiagramsMinVersion}get enablePromptEnhancer(){return this._enablePromptEnhancer}get enableDesignSystemRichTextEditor(){return this._enableDesignSystemRichTextEditor}get idleNewSessionNotificationTimeoutMs(){return this._idleNewSessionNotificationTimeoutMs??0}get idleNewSessionMessageTimeoutMs(){return this._idleNewSessionMessageTimeoutMs??0}get enableChatMultimodal(){return this._enableChatMultimodal}get enableAgentMode(){return this._enableAgentMode}get enableAgentAutoMode(){return this._enableAgentAutoMode}get enableRichCheckpointInfo(){return this._enableRichCheckpointInfo}get agentMemoriesFilePathName(){return this._agentMemoriesFilePathName}get conversationHistorySizeThresholdBytes(){return this._conversationHistorySizeThresholdBytes}get userTier(){return this._userTier}get eloModelConfiguration(){return this._eloModelConfiguration}get truncateChatHistory(){return this._truncateChatHistory}get enableBackgroundAgents(){return this._enableBackgroundAgents}get enableNewThreadsList(){return this._enableNewThreadsList}get customPersonalityPrompts(){return this._customPersonalityPrompts}get enablePersonalities(){return this._enablePersonalities||this._enableDebugFeatures}get enableRules(){return this._enableRules}get memoryClassificationOnFirstToken(){return this._memoryClassificationOnFirstToken}get modelRegistry(){return this._modelRegistry}get enableModelRegistry(){return this._enableModelRegistry}get enableTaskList(){return this._enableTaskList}get clientAnnouncement(){return this._clientAnnouncement}get useHistorySummary(){return this._useHistorySummary}get historySummaryParams(){return this._historySummaryParams}get enableExchangeStorage(){return this._enableExchangeStorage}get enableToolUseStateStorage(){return this._enableToolUseStateStorage}get retryChatStreamTimeouts(){return this._retryChatStreamTimeouts}get enableCommitIndexing(){return this._enableCommitIndexing}get enableMemoryRetrieval(){return this._enableMemoryRetrieval}get enableAgentTabs(){return this._enableAgentTabs}get isVscodeVersionOutdated(){return this._isVscodeVersionOutdated}get vscodeMinVersion(){return this._vscodeMinVersion}get enableErgonomicsUpdate(){return this._enableDebugFeatures}get enableGroupedTools(){return this._enableGroupedTools}get remoteAgentsResumeHintAvailableTtlDays(){return this._remoteAgentsResumeHintAvailableTtlDays}get enableParallelTools(){return this._enableParallelTools}get enableAgentGitTracker(){return this._enableAgentGitTracker}get memoriesParams(){return this._memoriesParams}}var Su=$a('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.84182 3.13514C9.04327 3.32401 9.05348 3.64042 8.86462 3.84188L5.43521 7.49991L8.86462 11.1579C9.05348 11.3594 9.04327 11.6758 8.84182 11.8647C8.64036 12.0535 8.32394 12.0433 8.13508 11.8419L4.38508 7.84188C4.20477 7.64955 4.20477 7.35027 4.38508 7.15794L8.13508 3.15794C8.32394 2.95648 8.64036 2.94628 8.84182 3.13514Z" fill="currentColor"></path></svg>');function Iu(e){var t=Su();A(e,t)}const ft=class ft{constructor(t=void 0){c(this,"_lastFocusAnchorElement");c(this,"_focusedIndexStore",Xn(void 0));c(this,"focusedIndex",this._focusedIndexStore);c(this,"_rootElement");c(this,"_triggerElement");c(this,"_getItems",()=>{var s;const t=(s=this._rootElement)==null?void 0:s.querySelectorAll(`.${ft.ITEM_CLASS}`),n=t==null?void 0:t[0];return n instanceof HTMLElement&&this._recomputeFocusAnchor(n),Array.from(t??[])});c(this,"_recomputeFocusAnchor",t=>{var r;const n=(r=this._parentContext)==null?void 0:r._getItems(),s=n==null?void 0:n.indexOf(t);if(s===void 0||n===void 0)return;const a=Math.max(s-1,0);this._lastFocusAnchorElement=n[a]});c(this,"registerRoot",t=>{this._rootElement=t,t.addEventListener("keydown",this._onKeyDown);const n=()=>{this.getCurrentFocusedIdx()},s=a=>{t.contains(a.relatedTarget)||this._focusedIndexStore.set(void 0)};return t.addEventListener("focusin",n),t.addEventListener("focusout",s),this._getItems(),{destroy:()=>{this._rootElement=void 0,t.removeEventListener("keydown",this._onKeyDown),t.removeEventListener("focusin",n),t.removeEventListener("focusout",s),this._focusedIndexStore.set(void 0)}}});c(this,"registerTrigger",t=>(this._triggerElement=t.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])')??t,{destroy:()=>{this._triggerElement=void 0}}));c(this,"_onKeyDown",t=>{var n;switch(t.key){case"ArrowUp":t.preventDefault(),this.focusPrev();break;case"ArrowDown":t.preventDefault(),this.focusNext();break;case"ArrowLeft":this._requestClose();break;case"ArrowRight":this.clickFocusedItem();break;case"Tab":{const s=this.getCurrentFocusedIdx();if(s===void 0||this.parentContext)break;(!t.shiftKey&&s===this._getItems().length-1||t.shiftKey&&s===0)&&(t.preventDefault(),(n=this._triggerElement)==null||n.focus());break}}});c(this,"_requestClose",()=>{var t;(t=this._rootElement)==null||t.dispatchEvent(new vo)});c(this,"getCurrentFocusedIdx",()=>{const t=this._getItems().findIndex(s=>s===document.activeElement),n=t===-1?void 0:t;return this._focusedIndexStore.set(n),n});c(this,"setFocusedIdx",t=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const s=Ot(t,n.length);this._focusedIndexStore.set(s)});c(this,"focusIdx",t=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const s=Ot(t,n.length),a=n[s];a==null||a.focus(),this._focusedIndexStore.set(s)});c(this,"popNestedFocus",()=>{if(this._parentContext){this._focusedIndexStore.set(void 0);const t=this._lastFocusAnchorElement,n=t?this._parentContext._getItems().indexOf(t):void 0;return n===void 0?(this._parentContext.focusIdx(0),!0):(this._parentContext.focusIdx(n),!0)}return!1});c(this,"focusNext",()=>{const t=this._getItems();if(t.length===0)return;const n=Ot(t.findIndex(s=>s===document.activeElement)+1,t.length);t[n].focus(),this._focusedIndexStore.set(n)});c(this,"focusPrev",()=>{var s;const t=this._getItems();if(t.length===0)return;const n=Ot(t.findIndex(a=>a===document.activeElement)-1,t.length);(s=t[n])==null||s.focus(),this._focusedIndexStore.set(n)});c(this,"clickFocusedItem",async()=>{const t=document.activeElement;t&&(t.click(),await Ln())});this._parentContext=t}get rootElement(){return this._rootElement}get triggerElement(){return this._triggerElement}get parentContext(){return this._parentContext}};c(ft,"CONTEXT_KEY","augment-dropdown-menu-focus"),c(ft,"ITEM_CLASS","js-dropdown-menu__focusable-item");let ie=ft;function Ot(e,t){return(e%t+t)%t}const Ze="augment-dropdown-menu-content";var Nu=J("<div><!></div>"),wu=J('<div class="l-dropdown-menu-augment__container svelte-o54ind"><!></div>');function Ua(e,t){me(t,!1);const[n,s]=Re(),a=()=>_e(g,"$sizeState",n),r=Ee();let o=I(t,"size",8,2),i=I(t,"onEscapeKeyDown",8,()=>{}),l=I(t,"onClickOutside",8,()=>{}),u=I(t,"onRequestClose",8,()=>{}),m=I(t,"side",8,"top"),d=I(t,"align",8,"center");const h={size:Xn(o())},g=h.size;qa(Ze,h);const f=Q(ie.CONTEXT_KEY),b=Q(Mt.CONTEXT_KEY);Te(()=>De(o()),()=>{g.set(o())}),Te(()=>{},()=>{fo(Se(r,b.state),"$openState",n)}),Qe(),he(),be("keydown",go,function(y){if(_e(M(r),"$openState",n).open&&y.key==="Tab"&&!y.shiftKey){if(f.getCurrentFocusedIdx()!==void 0)return;y.preventDefault(),f==null||f.focusIdx(0)}}),To(e,{onEscapeKeyDown:i(),onClickOutside:l(),onRequestClose:u(),get side(){return m()},get align(){return d()},$$events:{keydown(y){we.call(this,t,y)}},children:(y,v)=>{var C=wu(),N=re(C);Eo(N,{get size(){return a()},insetContent:!0,includeBackground:!1,children:(P,S)=>{var k=Nu(),R=re(k);X(R,t,"default",{},null),Pn(k,te=>{var ne;return(ne=f.registerRoot)==null?void 0:ne.call(f,te)}),et(()=>tt(k,1,`l-dropdown-menu-augment__contents l-dropdown-menu-augment__contents--size-${a()}`,"svelte-o54ind")),A(P,k)},$$slots:{default:!0}}),A(y,C)},$$slots:{default:!0}}),pe(),s()}var Cu=J('<div class="c-dropdown-menu-augment__item-icon svelte-48oly1"><!></div>'),ku=J('<div class="c-dropdown-menu-augment__item-icon svelte-48oly1"><!></div>'),Ru=J("<!> <!> <!>",1);function zn(e,t){const n=Ha(t),s=V(t,["children","$$slots","$$events","$$legacy"]),a=V(s,["highlight","disabled","color","onSelect"]);me(t,!1);const[r,o]=Re(),i=()=>_e(v,"$sizeState",r),l=Ee(),u=Ee(),m=Ee();let d=I(t,"highlight",24,()=>{}),h=I(t,"disabled",24,()=>{}),g=I(t,"color",24,()=>{}),f=I(t,"onSelect",8,()=>{});const b=Q(Ze),y=Q(ie.CONTEXT_KEY),v=b.size;function C(R){var T;if(h())return;const te=(T=y.rootElement)==null?void 0:T.querySelectorAll(`.${ie.ITEM_CLASS}`);if(!te)return;const ne=Array.from(te).findIndex(Ie=>Ie===R);ne!==-1&&y.setFocusedIdx(ne)}Te(()=>(M(l),M(u),De(a)),()=>{Se(l,a.class),Se(u,Va(a,["class"]))}),Te(()=>(De(h()),De(d()),M(l)),()=>{Se(m,[h()?"":ie.ITEM_CLASS,"c-dropdown-menu-augment__item",d()?"c-dropdown-menu-augment__item--highlighted":"",M(l)].join(" "))}),Qe(),he();const N=Bt(()=>g()??"neutral"),P=Bt(()=>!g());var S=_s(()=>vs("dropdown-menu-item","highlighted",d())),k=_s(()=>vs("dropdown-menu-item","disabled",h()));oo(e,He({get class(){return M(m)},get size(){return i()},variant:"ghost",get color(){return M(N)},get highContrast(){return M(P)},alignment:"left",get disabled(){return h()}},()=>M(S),()=>M(k),()=>M(u),{$$events:{click:R=>{R.currentTarget instanceof HTMLElement&&C(R.currentTarget),f()(R)},mouseover:R=>{R.currentTarget instanceof HTMLElement&&C(R.currentTarget)},mousedown:R=>{R.preventDefault(),R.stopPropagation()}},children:(R,te)=>{var ne=Ru(),T=ve(ne),Ie=U=>{var ce=Cu(),Ne=re(ce);X(Ne,t,"iconLeft",{},null),A(U,ce)};dt(T,U=>{Ge(()=>n.iconLeft)&&U(Ie)});var Ye=mt(T,2);Ga(Ye,{get size(){return i()},children:(U,ce)=>{var Ne=xe(),St=ve(Ne);X(St,t,"default",{},null),A(U,Ne)},$$slots:{default:!0}});var w=mt(Ye,2),ue=U=>{var ce=ku(),Ne=re(ce);X(Ne,t,"iconRight",{},null),A(U,ce)};dt(w,U=>{Ge(()=>n.iconRight)&&U(ue)}),A(R,ne)},$$slots:{default:!0}})),pe(),o()}var xu=$a("<svg><!></svg>");function Au(e,t){const n=V(t,["children","$$slots","$$events","$$legacy"]);var s=xu();Ba(s,()=>({xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16","data-ds-icon":"fa",viewBox:"0 0 16 16",...n}));var a=re(s);io(a,()=>'<path fill-opacity=".01" d="M0 0h16v16H0z"/><path fill-opacity=".365" d="M10.149 7.602a.56.56 0 0 1 0 .794l-3.5 3.502a.562.562 0 0 1-.795-.795L8.956 8 5.852 4.898a.562.562 0 0 1 .795-.795z"/>',!0),A(e,s)}function Pa(e,t){const n=V(t,["children","$$slots","$$events","$$legacy"]),s=V(n,[]);zn(e,He({class:"c-dropdown-menu-augment__breadcrumb-chevron"},()=>s,{children:(a,r)=>{var o=xe(),i=ve(o);X(i,t,"default",{},null),A(a,o)},$$slots:{default:!0,iconRight:(a,r)=>{Au(a,{slot:"iconRight"})}}}))}var Ou=J("<div><!></div>");function La(e,t){const n=V(t,["children","$$slots","$$events","$$legacy"]),s=V(n,["requestOpen","requestClose","focusIdx","setFocusedIdx","getCurrentFocusedIdx","focusedIndex","defaultOpen","open","onOpenChange","delayDurationMs","nested","onHoverStart","onHoverEnd","triggerOn"]);me(t,!1);let a=I(t,"defaultOpen",24,()=>{}),r=I(t,"open",24,()=>{}),o=I(t,"onOpenChange",24,()=>{}),i=I(t,"delayDurationMs",24,()=>{}),l=I(t,"nested",24,()=>{}),u=I(t,"onHoverStart",8,()=>{}),m=I(t,"onHoverEnd",8,()=>{}),d=I(t,"triggerOn",24,()=>[Dt.Click]),h=Ee();const g=()=>{var S;return(S=M(h))==null?void 0:S.requestOpen()},f=()=>{var S;return(S=M(h))==null?void 0:S.requestClose()},b=S=>N.focusIdx(S),y=S=>N.setFocusedIdx(S),v=()=>N.getCurrentFocusedIdx(),C=Q(ie.CONTEXT_KEY),N=new ie(C);qa(ie.CONTEXT_KEY,N);const P=N.focusedIndex;return he(),Ya(So(e,He({get defaultOpen(){return a()},get open(){return r()},get onOpenChange(){return o()},get delayDurationMs(){return i()},onHoverStart:u(),onHoverEnd:m(),get triggerOn(){return d()},get nested(){return l()}},()=>s,{children:(S,k)=>{var R=xe(),te=ve(R);X(te,t,"default",{},null),A(S,R)},$$slots:{default:!0},$$legacy:!0})),S=>Se(h,S),()=>M(h)),je(t,"requestOpen",g),je(t,"requestClose",f),je(t,"focusIdx",b),je(t,"setFocusedIdx",y),je(t,"getCurrentFocusedIdx",v),je(t,"focusedIndex",P),pe({requestOpen:g,requestClose:f,focusIdx:b,setFocusedIdx:y,getCurrentFocusedIdx:v,focusedIndex:P})}var Mu=J("<div></div>");function Du(e,t){let n=I(t,"size",8,1),s=I(t,"orientation",8,"horizontal"),a=I(t,"useCurrentColor",8,!1),r=I(t,"class",8,"");var o=Mu();let i;et(l=>i=tt(o,1,`c-separator c-separator--size-${n()===.5?"0_5":n()} c-separator--orientation-${s()} ${r()}`,"svelte-o0csoy",i,l),[()=>({"c-separator--current-color":a()})],Bt),A(e,o)}var Fu=J("<div><!></div>"),Uu=J('<label class="c-text-field-label svelte-vuqlvc"><!></label>'),Pu=J('<div class="c-text-field__slot c-base-text-input__slot"><!></div>'),Lu=J('<div class="c-text-field__slot c-base-text-input__slot"><!></div>'),$u=J("<!> <input/> <!>",1),qu=J("<div><!> <!></div>");function Hu(e,t){const n=Ha(t),s=V(t,["children","$$slots","$$events","$$legacy"]),a=V(s,["variant","size","color","textInput","value","id"]);me(t,!1);const r=Ee(),o=Ee(),i=Ee(),l=bo();let u=I(t,"variant",8,"surface"),m=I(t,"size",8,2),d=I(t,"color",24,()=>{}),h=I(t,"textInput",28,()=>{}),g=I(t,"value",12,""),f=I(t,"id",24,()=>{});const b=`text-field-${Math.random().toString(36).substring(2,11)}`;function y(S){l("change",S)}Te(()=>De(f()),()=>{Se(r,f()||b)}),Te(()=>(M(o),M(i),De(a)),()=>{Se(o,a.class),Se(i,Va(a,["class"]))}),Qe(),he();var v=qu();tt(v,1,"c-text-field svelte-vuqlvc",null,{},{"c-text-field--has-left-icon":n.iconLeft!==void 0,"c-text-field--has-right-icon":n.iconRight!==void 0});var C=re(v),N=S=>{var k=Uu(),R=re(k);X(R,t,"label",{},null),et(()=>_o(k,"for",M(r))),A(S,k)};dt(C,S=>{Ge(()=>n.label)&&S(N)});var P=mt(C,2);Io(P,{get variant(){return u()},get size(){return m()},get color(){return d()},children:(S,k)=>{var R=$u(),te=ve(R),ne=w=>{var ue=Pu(),U=re(ue);X(U,t,"iconLeft",{},null),A(w,ue)};dt(te,w=>{Ge(()=>n.iconLeft)&&w(ne)});var T=mt(te,2);Ba(T,()=>({spellCheck:"false",class:`c-text-field__input c-base-text-input__input ${M(o)}`,id:M(r),...M(i)}),void 0,"svelte-vuqlvc"),Ya(T,w=>h(w),()=>h());var Ie=mt(T,2),Ye=w=>{var ue=Lu(),U=re(ue);X(U,t,"iconRight",{},null),A(w,ue)};dt(Ie,w=>{Ge(()=>n.iconRight)&&w(Ye)}),No(T,g),be("change",T,y),be("click",T,function(w){we.call(this,t,w)}),be("keydown",T,function(w){we.call(this,t,w)}),be("input",T,function(w){we.call(this,t,w)}),be("blur",T,function(w){we.call(this,t,w)}),be("dblclick",T,function(w){we.call(this,t,w)}),be("focus",T,function(w){we.call(this,t,w)}),be("mouseup",T,function(w){we.call(this,t,w)}),be("selectionchange",T,function(w){we.call(this,t,w)}),A(S,R)},$$slots:{default:!0}}),A(e,v),pe()}var Gu=J("<div><!></div>"),Vu=J("<div><!></div>");const Lc={BreadcrumbBackItem:function(e,t){const n=V(t,["children","$$slots","$$events","$$legacy"]),s=V(n,[]);zn(e,He({class:"c-dropdown-menu-augment__breadcrumb-back-chevron"},()=>s,{children:(a,r)=>{var o=xe(),i=ve(o);X(i,t,"default",{},null),A(a,o)},$$slots:{default:!0,iconLeft:(a,r)=>{Iu(a)}}}))},BreadcrumbItem:Pa,Content:Ua,Item:zn,Label:function(e,t){me(t,!1);const[n,s]=Re(),a=()=>_e(o,"$sizeState",n),r=Ee(),o=Q(Ze).size;Te(()=>a(),()=>{Se(r,["c-dropdown-menu-augment__label-item",`c-dropdown-menu-augment__label-item--size-${a()}`].join(" "))}),Qe(),he();var i=Ou(),l=re(i);Ga(l,{get size(){return a()},weight:"regular",children:(u,m)=>{var d=xe(),h=ve(d);X(h,t,"default",{},null),A(u,d)},$$slots:{default:!0}}),et(()=>tt(i,1,Ts(M(r)),"svelte-gehsvg")),A(e,i),pe(),s()},Root:La,Separator:function(e,t){me(t,!1);const[n,s]=Re(),a=Q(Ze).size;he();var r=Fu();Du(re(r),{size:4,orientation:"horizontal"}),et(()=>tt(r,1,`c-dropdown-menu-augment__separator c-dropdown-menu-augment__separator--size-${_e(a,"$sizeState",n)}`,"svelte-24h9u")),A(e,r),pe(),s()},Sub:function(e,t){const n=V(t,["children","$$slots","$$events","$$legacy"]),s=V(n,[]);me(t,!1),he();const a=Bt(()=>(De(Dt),Ge(()=>[Dt.Click,Dt.Hover])));La(e,He({nested:!0,get triggerOn(){return M(a)}},()=>s,{children:(r,o)=>{var i=xe(),l=ve(i);X(l,t,"default",{},null),A(r,i)},$$slots:{default:!0}})),pe()},SubContent:function(e,t){const n=V(t,["children","$$slots","$$events","$$legacy"]),s=V(n,[]);me(t,!1);const[a,r]=Re(),o=()=>_e(m,"$didOpen",a),i=Q(Ze).size,l=Q(ie.CONTEXT_KEY),u=Q(Mt.CONTEXT_KEY),m=yo(u.state,d=>d.open);Te(()=>(o(),Ln),()=>{o()&&Ln().then(()=>l==null?void 0:l.focusIdx(0))}),Te(()=>o(),()=>{!o()&&(l==null||l.popNestedFocus())}),Qe(),he(),Ua(e,He(()=>s,{side:"right",align:"start",get size(){return _e(i,"$sizeState",a)},children:(d,h)=>{var g=xe(),f=ve(g);X(f,t,"default",{},null),A(d,g)},$$slots:{default:!0}})),pe(),r()},SubTrigger:function(e,t){me(t,!1);const[n,s]=Re(),a=Q(Mt.CONTEXT_KEY).state;he(),Es(e,{children:(r,o)=>{Pa(r,{get highlight(){return _e(a,"$stateStore",n).open},children:(i,l)=>{var u=xe(),m=ve(u);X(m,t,"default",{},null),A(i,u)},$$slots:{default:!0}})},$$slots:{default:!0}}),pe(),s()},TextFieldItem:function(e,t){const n=V(t,["children","$$slots","$$events","$$legacy"]),s=V(n,["value"]);me(t,!1);const[a,r]=Re(),o=()=>_e(u,"$sizeState",a),i=Ee();let l=I(t,"value",12,"");const u=Q(Ze).size;Te(()=>o(),()=>{Se(i,["c-dropdown-menu-augment__text-field-item",`c-dropdown-menu-augment__text-field-item--size-${o()}`].join(" "))}),Qe(),he();var m=Gu();Hu(re(m),He({get class(){return De(ie),Ge(()=>ie.ITEM_CLASS)},get size(){return o()}},()=>s,{get value(){return l()},set value(d){l(d)},$$legacy:!0})),et(()=>tt(m,1,Ts(M(i)),"svelte-1xu00bc")),A(e,m),pe(),r()},Trigger:function(e,t){me(t,!1);const[n,s]=Re(),a=()=>_e(l,"$openState",n);let r=I(t,"referenceClientRect",24,()=>{});const o=Q(ie.CONTEXT_KEY),i=Q(Mt.CONTEXT_KEY),l=i.state;he(),Es(e,{get referenceClientRect(){return r()},$$events:{keydown:async u=>{switch(u.key){case"ArrowUp":u.preventDefault(),u.stopPropagation(),a().open||await o.clickFocusedItem(),o==null||o.focusIdx(-1);break;case"ArrowDown":u.preventDefault(),u.stopPropagation(),a().open||await o.clickFocusedItem(),o==null||o.focusIdx(0);break;case"Enter":u.preventDefault(),u.stopPropagation(),o==null||o.clickFocusedItem()}}},children:(u,m)=>{var d=Vu(),h=re(d);X(h,t,"default",{},null),Pn(d,g=>{var f;return(f=o.registerTrigger)==null?void 0:f.call(o,g)}),Pn(d,g=>{var f;return(f=i.registerTrigger)==null?void 0:f.call(i,g)}),A(u,d)},$$slots:{default:!0}}),pe(),s()}};export{lc as $,Qu as A,ac as B,Pc as C,Lc as D,Mc as E,Oi as F,ou as G,uc as H,ll as I,qr as J,cl as K,dl as L,Mi as M,vl as N,ml as O,pl as P,hl as Q,to as R,z as S,Hu as T,zn as U,dc as V,cc as W,nc as X,Fr as Y,oc as Z,ic as _,fa as a,Ji as a$,sc as a0,ul as a1,gl as a2,q as a3,Au as a4,Di as a5,Sc as a6,Bi as a7,pc as a8,$e as a9,Wi as aA,oe as aB,wc as aC,xc as aD,Gt as aE,Fa as aF,Iu as aG,Ki as aH,ji as aI,Je as aJ,Yi as aK,hc as aL,Lr as aM,$i as aN,gc as aO,su as aP,Xr as aQ,bc as aR,yc as aS,ys as aT,au as aU,ru as aV,Xt as aW,_l as aX,$r as aY,vc as aZ,_c as a_,Pr as aa,Pi as ab,Tl as ac,Ui as ad,Ma as ae,Gi as af,Vi as ag,bl as ah,yl as ai,mc as aj,fc as ak,Nc as al,Uc as am,ec as an,rc as ao,Du as ap,zt as aq,Ec as ar,fu as as,Ic as at,yu as au,gt as av,kc as aw,Rc as ax,Cc as ay,zi as az,qi as b,Xi as b0,Oc as b1,Ac as b2,E as c,x as d,Hi as e,Li as f,tl as g,nl as h,L as i,Xe as j,gu as k,se as l,Eu as m,Fc as n,Dc as o,Fn as p,it as q,Qi as r,Tc as s,el as t,sl as u,al as v,tc as w,rl as x,ol as y,il as z};
