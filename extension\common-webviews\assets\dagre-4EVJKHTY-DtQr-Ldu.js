import{_ as w,av as M,aw as L,ax as j,ay as Y,l as o,c as H,az as z,aA as _,ah as q,am as K,ai as O,ag as Q,aB as U,aC as V,aD as W}from"./AugmentMessage-CsMcb4z_.js";import{G as k}from"./graph-BMpwc83L.js";import{l as Z}from"./layout-DDlpRwC-.js";import{i as S}from"./_baseUniq-Cztm49Po.js";import{c as $}from"./clone-Ni77tEaN.js";import{m as B}from"./_basePickBy-D39FAcbI.js";import"./SpinnerAugment-kH3m-zOb.js";import"./IconButtonAugment-C8Qb_O9b.js";import"./CalloutAugment-BUpCn_TI.js";import"./CardAugment-BAmr_-U4.js";import"./index-BnlWKkvq.js";import"./async-messaging-CCLqHBoR.js";import"./message-broker-CwcPcQ_e.js";import"./types-CGlLNakm.js";import"./file-paths-Bl_IgYJd.js";import"./BaseTextInput-TeF8u93x.js";import"./folder-opened-D9klsFkp.js";import"./index-D-fDrvnq.js";import"./diff-operations-DJ1OAK7V.js";import"./svelte-component-B6upIdsM.js";import"./Filespan-B9x3U15q.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./index-B_qLJJaQ.js";import"./keypress-DD1aQVr0.js";import"./await-BH7XNauH.js";import"./OpenFileButton-CgBtRJfx.js";import"./chat-context-nrEZUHNl.js";import"./index-C4gKbsWy.js";import"./remote-agents-client-TVS4i5h_.js";import"./ra-diff-ops-model-dZccOtNT.js";import"./TextAreaAugment-IsRYkfgU.js";import"./ButtonAugment-BZfc2Zk1.js";import"./CollapseButtonAugment-DNisU6cu.js";import"./user-DigCn7eq.js";import"./MaterialIcon-Cs9y5rDt.js";import"./CopyButton-BDqSrh0t.js";import"./ellipsis-2hnmWCsv.js";import"./IconFilePath-C18xEJF3.js";import"./LanguageIcon-Bn3MfWRN.js";import"./next-edit-types-904A5ehg.js";import"./chevron-down-BbSBSK7f.js";import"./index-XEt2J8A6.js";import"./augment-logo-C7lllHv9.js";import"./pen-to-square-CYPFKWo4.js";import"./check-C8b2LRem.js";function X(e){var r={options:{directed:e.isDirected(),multigraph:e.isMultigraph(),compound:e.isCompound()},nodes:ee(e),edges:ne(e)};return S(e.graph())||(r.value=$(e.graph())),r}function ee(e){return B(e.nodes(),function(r){var n=e.node(r),a=e.parent(r),d={v:r};return S(n)||(d.value=n),S(a)||(d.parent=a),d})}function ne(e){return B(e.edges(),function(r){var n=e.edge(r),a={v:r.v,w:r.w};return S(r.name)||(a.name=r.name),S(n)||(a.value=n),a})}var l=new Map,b=new Map,P=new Map,re=w(()=>{b.clear(),P.clear(),l.clear()},"clear"),D=w((e,r)=>{const n=b.get(r)||[];return o.trace("In isDescendant",r," ",e," = ",n.includes(e)),n.includes(e)},"isDescendant"),te=w((e,r)=>{const n=b.get(r)||[];return o.info("Descendants of ",r," is ",n),o.info("Edge is ",e),e.v!==r&&e.w!==r&&(n?n.includes(e.v)||D(e.v,r)||D(e.w,r)||n.includes(e.w):(o.debug("Tilt, ",r,",not in descendants"),!1))},"edgeInCluster"),A=w((e,r,n,a)=>{o.warn("Copying children of ",e,"root",a,"data",r.node(e),a);const d=r.children(e)||[];e!==a&&d.push(e),o.warn("Copying (nodes) clusterId",e,"nodes",d),d.forEach(s=>{if(r.children(s).length>0)A(s,r,n,a);else{const i=r.node(s);o.info("cp ",s," to ",a," with parent ",e),n.setNode(s,i),a!==r.parent(s)&&(o.warn("Setting parent",s,r.parent(s)),n.setParent(s,r.parent(s))),e!==a&&s!==e?(o.debug("Setting parent",s,e),n.setParent(s,e)):(o.info("In copy ",e,"root",a,"data",r.node(e),a),o.debug("Not Setting parent for node=",s,"cluster!==rootId",e!==a,"node!==clusterId",s!==e));const c=r.edges(s);o.debug("Copying Edges",c),c.forEach(p=>{o.info("Edge",p);const E=r.edge(p.v,p.w,p.name);o.info("Edge data",E,a);try{te(p,a)?(o.info("Copying as ",p.v,p.w,E,p.name),n.setEdge(p.v,p.w,E,p.name),o.info("newGraph edges ",n.edges(),n.edge(n.edges()[0]))):o.info("Skipping copy of edge ",p.v,"-->",p.w," rootId: ",a," clusterId:",e)}catch(C){o.error(C)}})}o.debug("Removing node",s),r.removeNode(s)})},"copy"),J=w((e,r)=>{const n=r.children(e);let a=[...n];for(const d of n)P.set(d,e),a=[...a,...J(d,r)];return a},"extractDescendants"),oe=w((e,r,n)=>{const a=e.edges().filter(c=>c.v===r||c.w===r),d=e.edges().filter(c=>c.v===n||c.w===n),s=a.map(c=>({v:c.v===r?n:c.v,w:c.w===r?r:c.w})),i=d.map(c=>({v:c.v,w:c.w}));return s.filter(c=>i.some(p=>c.v===p.v&&c.w===p.w))},"findCommonEdges"),I=w((e,r,n)=>{const a=r.children(e);if(o.trace("Searching children of id ",e,a),a.length<1)return e;let d;for(const s of a){const i=I(s,r,n),c=oe(r,n,i);if(i){if(!(c.length>0))return i;d=i}}return d},"findNonClusterChild"),G=w(e=>l.has(e)&&l.get(e).externalConnections&&l.has(e)?l.get(e).id:e,"getAnchorId"),ie=w((e,r)=>{if(!e||r>10)o.debug("Opting out, no graph ");else{o.debug("Opting in, graph "),e.nodes().forEach(function(n){e.children(n).length>0&&(o.warn("Cluster identified",n," Replacement id in edges: ",I(n,e,n)),b.set(n,J(n,e)),l.set(n,{id:I(n,e,n),clusterData:e.node(n)}))}),e.nodes().forEach(function(n){const a=e.children(n),d=e.edges();a.length>0?(o.debug("Cluster identified",n,b),d.forEach(s=>{D(s.v,n)^D(s.w,n)&&(o.warn("Edge: ",s," leaves cluster ",n),o.warn("Descendants of XXX ",n,": ",b.get(n)),l.get(n).externalConnections=!0)})):o.debug("Not a cluster ",n,b)});for(let n of l.keys()){const a=l.get(n).id,d=e.parent(a);d!==n&&l.has(d)&&!l.get(d).externalConnections&&(l.get(n).id=d)}e.edges().forEach(function(n){const a=e.edge(n);o.warn("Edge "+n.v+" -> "+n.w+": "+JSON.stringify(n)),o.warn("Edge "+n.v+" -> "+n.w+": "+JSON.stringify(e.edge(n)));let d=n.v,s=n.w;if(o.warn("Fix XXX",l,"ids:",n.v,n.w,"Translating: ",l.get(n.v)," --- ",l.get(n.w)),l.get(n.v)||l.get(n.w)){if(o.warn("Fixing and trying - removing XXX",n.v,n.w,n.name),d=G(n.v),s=G(n.w),e.removeEdge(n.v,n.w,n.name),d!==n.v){const i=e.parent(d);l.get(i).externalConnections=!0,a.fromCluster=n.v}if(s!==n.w){const i=e.parent(s);l.get(i).externalConnections=!0,a.toCluster=n.w}o.warn("Fix Replacing with XXX",d,s,n.name),e.setEdge(d,s,a,n.name)}}),o.warn("Adjusted Graph",X(e)),R(e,0),o.trace(l)}},"adjustClustersAndEdges"),R=w((e,r)=>{var d,s;if(o.warn("extractor - ",r,X(e),e.children("D")),r>10)return void o.error("Bailing out");let n=e.nodes(),a=!1;for(const i of n){const c=e.children(i);a=a||c.length>0}if(a){o.debug("Nodes = ",n,r);for(const i of n)if(o.debug("Extracting node",i,l,l.has(i)&&!l.get(i).externalConnections,!e.parent(i),e.node(i),e.children("D")," Depth ",r),l.has(i))if(!l.get(i).externalConnections&&e.children(i)&&e.children(i).length>0){o.warn("Cluster without external connections, without a parent and with children",i,r);let c=e.graph().rankdir==="TB"?"LR":"TB";(s=(d=l.get(i))==null?void 0:d.clusterData)!=null&&s.dir&&(c=l.get(i).clusterData.dir,o.warn("Fixing dir",l.get(i).clusterData.dir,c));const p=new k({multigraph:!0,compound:!0}).setGraph({rankdir:c,nodesep:50,ranksep:50,marginx:8,marginy:8}).setDefaultEdgeLabel(function(){return{}});o.warn("Old graph before copy",X(e)),A(i,e,p,i),e.setNode(i,{clusterNode:!0,id:i,clusterData:l.get(i).clusterData,label:l.get(i).label,graph:p}),o.warn("New graph after copy node: (",i,")",X(p)),o.debug("Old graph after copy",X(e))}else o.warn("Cluster ** ",i," **not meeting the criteria !externalConnections:",!l.get(i).externalConnections," no parent: ",!e.parent(i)," children ",e.children(i)&&e.children(i).length>0,e.children("D"),r),o.debug(l);else o.debug("Not a cluster",i,r);n=e.nodes(),o.warn("New list of nodes",n);for(const i of n){const c=e.node(i);o.warn(" Now next level",i,c),c!=null&&c.clusterNode&&R(c.graph,r+1)}}else o.debug("Done, no node has children",e.nodes())},"extractor"),T=w((e,r)=>{if(r.length===0)return[];let n=Object.assign([],r);return r.forEach(a=>{const d=e.children(a),s=T(e,d);n=[...n,...s]}),n},"sorter"),ae=w(e=>T(e,e.children()),"sortNodesByHierarchy"),F=w(async(e,r,n,a,d,s)=>{o.warn("Graph in recursive render:XAX",X(r),d);const i=r.graph().rankdir;o.trace("Dir in recursive render - dir:",i);const c=e.insert("g").attr("class","root");r.nodes()?o.info("Recursive render XXX",r.nodes()):o.info("No nodes found for",r),r.edges().length>0&&o.info("Recursive edges",r.edge(r.edges()[0]));const p=c.insert("g").attr("class","clusters"),E=c.insert("g").attr("class","edgePaths"),C=c.insert("g").attr("class","edgeLabels"),f=c.insert("g").attr("class","nodes");await Promise.all(r.nodes().map(async function(g){const t=r.node(g);if(d!==void 0){const u=JSON.parse(JSON.stringify(d.clusterData));o.trace(`Setting data for parent cluster XXX
 Node.id = `,g,`
 data=`,u.height,`
Parent cluster`,d.height),r.setNode(d.id,u),r.parent(g)||(o.trace("Setting parent",g,d.id),r.setParent(g,d.id,u))}if(o.info("(Insert) Node XXX"+g+": "+JSON.stringify(r.node(g))),t==null?void 0:t.clusterNode){o.info("Cluster identified XBX",g,t.width,r.node(g));const{ranksep:u,nodesep:m}=r.graph();t.graph.setGraph({...t.graph.graph(),ranksep:u+25,nodesep:m});const N=await F(f,t.graph,n,a,r.node(g),s),x=N.elem;z(t,x),t.diff=N.diff||0,o.info("New compound node after recursive render XAX",g,"width",t.width,"height",t.height),_(x,t)}else r.children(g).length>0?(o.trace("Cluster - the non recursive path XBX",g,t.id,t,t.width,"Graph:",r),o.trace(I(t.id,r)),l.set(t.id,{id:I(t.id,r),node:t})):(o.trace("Node - the non recursive path XAX",g,f,r.node(g),i),await q(f,r.node(g),{config:s,dir:i}))})),await w(async()=>{const g=r.edges().map(async function(t){const u=r.edge(t.v,t.w,t.name);o.info("Edge "+t.v+" -> "+t.w+": "+JSON.stringify(t)),o.info("Edge "+t.v+" -> "+t.w+": ",t," ",JSON.stringify(r.edge(t))),o.info("Fix",l,"ids:",t.v,t.w,"Translating: ",l.get(t.v),l.get(t.w)),await W(C,u)});await Promise.all(g)},"processEdges")(),o.info("Graph before layout:",JSON.stringify(X(r))),o.info("############################################# XXX"),o.info("###                Layout                 ### XXX"),o.info("############################################# XXX"),Z(r),o.info("Graph after layout:",JSON.stringify(X(r)));let y=0,{subGraphTitleTotalMargin:v}=K(s);return await Promise.all(ae(r).map(async function(g){var u;const t=r.node(g);if(o.info("Position XBX => "+g+": ("+t.x,","+t.y,") width: ",t.width," height: ",t.height),t==null?void 0:t.clusterNode)t.y+=v,o.info("A tainted cluster node XBX1",g,t.id,t.width,t.height,t.x,t.y,r.parent(g)),l.get(t.id).node=t,O(t);else if(r.children(g).length>0){o.info("A pure cluster node XBX1",g,t.id,t.x,t.y,t.width,t.height,r.parent(g)),t.height+=v,r.node(t.parentId);const m=(t==null?void 0:t.padding)/2||0,N=((u=t==null?void 0:t.labelBBox)==null?void 0:u.height)||0,x=N-m||0;o.debug("OffsetY",x,"labelHeight",N,"halfPadding",m),await Q(p,t),l.get(t.id).node=t}else{const m=r.node(t.parentId);t.y+=v/2,o.info("A regular node XBX1 - using the padding",t.id,"parent",t.parentId,t.width,t.height,t.x,t.y,"offsetY",t.offsetY,"parent",m,m==null?void 0:m.offsetY,t),O(t)}})),r.edges().forEach(function(g){const t=r.edge(g);o.info("Edge "+g.v+" -> "+g.w+": "+JSON.stringify(t),t),t.points.forEach(x=>x.y+=v/2);const u=r.node(g.v);var m=r.node(g.w);const N=U(E,t,l,n,u,m,a);V(t,N)}),r.nodes().forEach(function(g){const t=r.node(g);o.info(g,t.type,t.diff),t.isGroup&&(y=t.diff)}),o.warn("Returning from recursive render XAX",c,y),{elem:c,diff:y}},"recursiveRender"),$e=w(async(e,r)=>{var s,i,c,p,E,C;const n=new k({multigraph:!0,compound:!0}).setGraph({rankdir:e.direction,nodesep:((s=e.config)==null?void 0:s.nodeSpacing)||((c=(i=e.config)==null?void 0:i.flowchart)==null?void 0:c.nodeSpacing)||e.nodeSpacing,ranksep:((p=e.config)==null?void 0:p.rankSpacing)||((C=(E=e.config)==null?void 0:E.flowchart)==null?void 0:C.rankSpacing)||e.rankSpacing,marginx:8,marginy:8}).setDefaultEdgeLabel(function(){return{}}),a=r.select("g");M(a,e.markers,e.type,e.diagramId),L(),j(),Y(),re(),e.nodes.forEach(f=>{n.setNode(f.id,{...f}),f.parentId&&n.setParent(f.id,f.parentId)}),o.debug("Edges:",e.edges),e.edges.forEach(f=>{if(f.start===f.end){const h=f.start,y=h+"---"+h+"---1",v=h+"---"+h+"---2",g=n.node(h);n.setNode(y,{domId:y,id:y,parentId:g.parentId,labelStyle:"",label:"",padding:0,shape:"labelRect",style:"",width:10,height:10}),n.setParent(y,g.parentId),n.setNode(v,{domId:v,id:v,parentId:g.parentId,labelStyle:"",padding:0,shape:"labelRect",label:"",style:"",width:10,height:10}),n.setParent(v,g.parentId);const t=structuredClone(f),u=structuredClone(f),m=structuredClone(f);t.label="",t.arrowTypeEnd="none",t.id=h+"-cyclic-special-1",u.arrowTypeEnd="none",u.id=h+"-cyclic-special-mid",m.label="",g.isGroup&&(t.fromCluster=h,m.toCluster=h),m.id=h+"-cyclic-special-2",n.setEdge(h,y,t,h+"-cyclic-special-0"),n.setEdge(y,v,u,h+"-cyclic-special-1"),n.setEdge(v,h,m,h+"-cyc<lic-special-2")}else n.setEdge(f.start,f.end,{...f},f.id)}),o.warn("Graph at first:",JSON.stringify(X(n))),ie(n),o.warn("Graph after XAX:",JSON.stringify(X(n)));const d=H();await F(a,n,e.type,e.diagramId,void 0,d)},"render");export{$e as render};
