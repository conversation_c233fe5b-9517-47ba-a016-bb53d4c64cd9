import{z as q,l as z,B as i,a4 as D,K as u,O as g,P as h,t as c,Q as p,Y as E,a5 as F,b as r,R as m,T as G,V as J,N as M,H as y,_ as C}from"./SpinnerAugment-kH3m-zOb.js";import{B as S,b as e}from"./IconButtonAugment-C8Qb_O9b.js";var U=u('<div class="c-button--icon svelte-14satks"><!></div>'),W=u('<div class="c-button--text svelte-14satks"><!></div>'),X=u('<div class="c-button--icon svelte-14satks"><!></div>'),Z=u("<div><!> <!> <!></div>");function et(x,t){const d=q(t),B=z(t,["children","$$slots","$$events","$$legacy"]),R=z(B,["size","variant","color","highContrast","disabled","radius","loading","alignment"]);let o=i(t,"size",8,2),$=i(t,"variant",8,"solid"),L=i(t,"color",8,"neutral"),H=i(t,"highContrast",8,!1),K=i(t,"disabled",8,!1),N=i(t,"radius",8,"medium"),O=i(t,"loading",8,!1),j=i(t,"alignment",8,"center");S(x,D({get size(){return o()},get variant(){return $()},get color(){return L()},get highContrast(){return H()},get disabled(){return K()},get loading(){return O()},get alignment(){return j()},get radius(){return N()}},()=>R,{$$events:{click(s){e.call(this,t,s)},keyup(s){e.call(this,t,s)},keydown(s){e.call(this,t,s)},mousedown(s){e.call(this,t,s)},mouseover(s){e.call(this,t,s)},focus(s){e.call(this,t,s)},mouseleave(s){e.call(this,t,s)},blur(s){e.call(this,t,s)},contextmenu(s){e.call(this,t,s)}},children:(s,w)=>{var v=Z(),b=c(v),A=a=>{var l=U(),n=c(l);m(n,t,"iconLeft",{},null),r(a,l)};g(b,a=>{h(()=>d.iconLeft)&&a(A)});var f=p(b,2),I=a=>{var l=W(),n=c(l);const T=C(()=>o()===.5?1:o()),V=C(()=>$()==="ghost"?"regular":"medium");G(n,{get size(){return y(T)},get weight(){return y(V)},children:(Y,tt)=>{var k=J(),_=M(k);m(_,t,"default",{},null),r(Y,k)},$$slots:{default:!0}}),r(a,l)};g(f,a=>{h(()=>d.default)&&a(I)});var P=p(f,2),Q=a=>{var l=X(),n=c(l);m(n,t,"iconRight",{},null),r(a,l)};g(P,a=>{h(()=>d.iconRight)&&a(Q)}),E(()=>F(v,1,`c-button--content c-button--size-${o()}`,"svelte-14satks")),r(s,v)},$$slots:{default:!0}}))}export{et as B};
