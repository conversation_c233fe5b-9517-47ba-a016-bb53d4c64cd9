import{A as I,B as o,m as u,al as ee,H as e,D as i,C as M,G as w,I as ae,J as ie,K as j,ab as y,am as J,a6 as K,t as p,R as L,an as Q,Q as B,O as te,Y as se,_ as re,a5 as T,b as Y,S as de}from"./SpinnerAugment-kH3m-zOb.js";import{a as ne,I as oe}from"./IconButtonAugment-C8Qb_O9b.js";import{t as le,f as ce}from"./index-XEt2J8A6.js";import{E as ve}from"./ellipsis-2hnmWCsv.js";const O=(_,{onResize:t,options:g})=>{const v=new ResizeObserver(t);return v.observe(_,g),{destroy(){v.unobserve(_),v.disconnect()}}};var me=j('<div class="c-drawer__hidden-indicator svelte-18f0m3o"><!></div>'),ue=j('<div><div class="c-drawer__left svelte-18f0m3o"><div class="c-drawer__left-content svelte-18f0m3o"><!></div></div> <div aria-hidden="true"></div> <div class="c-drawer__right svelte-18f0m3o"><!></div> <!></div>');function ge(_,t){I(t,!1);let g,v,S=o(t,"initialWidth",8,300),z=o(t,"expandedMinWidth",8,50),X=o(t,"minimizedWidth",8,0),s=o(t,"minimized",12,!1),q=o(t,"class",8,""),F=o(t,"showButton",8,!0),N=o(t,"deadzone",8,0),P=o(t,"columnLayoutThreshold",8,600),d=o(t,"layoutMode",28,()=>{}),x=u(),f=u(),l=u(!1),c=u(S()),R=u(S()),r=u(!1);function C(){s(!s())}function k(){if(e(f)){if(d()!==void 0)return i(r,d()==="column"),void(e(r)&&i(l,!1));i(r,e(f).clientWidth<P()),e(r)&&i(l,!1)}}ee(k),M(()=>(w(s()),w(d())),()=>{s()?(d("row"),i(r,!1)):d()!=="row"||s()||(d(void 0),k())}),M(()=>(w(d()),e(r)),()=>{d()!==void 0&&(i(r,d()==="column"),e(r)&&i(l,!1))}),M(()=>(w(s()),w(X()),e(c)),()=>{i(R,s()?X():e(c))}),ae(),ie();var h=ue();let E;y("mousemove",J,function(a){if(!e(l)||!e(x)||e(r))return;const n=a.clientX-g,b=e(f).clientWidth-200,m=v+n;m<z()?m<z()-N()?s(!0):(i(c,z()),s(!1)):m>b?(i(c,b),s(!1)):(i(c,m),s(!1))}),y("mouseup",J,function(){i(l,!1),i(c,Math.max(e(c),z()))});var W=p(h),D=p(W);K(D,"",{},{width:"var(--augment-drawer-width)","min-width":"var(--augment-drawer-width)","max-width":"var(--augment-drawer-width)"});var U=p(D);L(U,t,"left",{},null),Q(W,a=>i(x,a),()=>e(x));var $=B(W,2);let G;var H=B($,2),V=p(H);L(V,t,"right",{},null);var Z=B(H,2),A=a=>{var n=me(),b=p(n);oe(b,{variant:"solid",color:"accent",size:2,radius:"full",title:"Show panel",$$events:{click:C},children:(m,fe)=>{ve(m,{})},$$slots:{default:!0}}),le(3,n,()=>ce,()=>({y:0,x:0,duration:200})),Y(a,n)};te(Z,a=>{s()&&F()&&a(A)}),Q(h,a=>i(f,a),()=>e(f)),ne(h,(a,n)=>O==null?void 0:O(a,n),()=>({onResize:()=>d()===void 0&&k()})),se((a,n)=>{E=T(h,1,`c-drawer ${q()??""}`,"svelte-18f0m3o",E,a),K(W,`--augment-drawer-width:${e(R)??""}px;`),D.inert=e(l),G=T($,1,"c-drawer__handle svelte-18f0m3o",null,G,n)},[()=>({"is-dragging":e(l),"is-hidden":!e(R),"is-column":e(r)}),()=>({"is-locked":e(r)})],re),y("mousedown",$,function(a){e(r)||(i(l,!0),g=a.clientX,v=e(x).offsetWidth,a.preventDefault())}),y("dblclick",$,C),Y(_,h),de()}export{ge as D,O as r};
