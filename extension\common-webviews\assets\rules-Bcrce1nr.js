import{A as J,B as ne,m as b,C as N,D as f,G as L,H as e,I as ce,J as K,K as C,t as g,Q as M,T as B,X as D,b as n,V as de,N as pe,O as P,P as ue,S as Q,al as ve,ab as me,am as fe,W as ge,w as he,$ as $e,aB as ye}from"./SpinnerAugment-kH3m-zOb.js";import"./design-system-init-IgXUmngh.js";import{c as w,W as O}from"./IconButtonAugment-C8Qb_O9b.js";import{B as be}from"./ButtonAugment-BZfc2Zk1.js";import{O as we}from"./OpenFileButton-CgBtRJfx.js";import{C as Ee,E as Re,T as ze,a as S}from"./index-BnlWKkvq.js";import{M as U,R as V}from"./message-broker-CwcPcQ_e.js";import{R as Me,M as Ce}from"./rules-model-Cj5etjy1.js";import{R as Fe}from"./RulesModeSelector-Bn5tP_nz.js";import{C as Te}from"./chevron-left-DbIseCK3.js";import{T as ke}from"./CardAugment-BAmr_-U4.js";import{l as xe}from"./chevron-down-BbSBSK7f.js";import"./chat-context-nrEZUHNl.js";import"./index-C4gKbsWy.js";import"./index-D-fDrvnq.js";import"./remote-agents-client-TVS4i5h_.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-dZccOtNT.js";import"./TextAreaAugment-IsRYkfgU.js";import"./BaseTextInput-TeF8u93x.js";import"./async-messaging-CCLqHBoR.js";import"./file-paths-Bl_IgYJd.js";var Ne=C('<div class="c-rule-config svelte-1r8al3d"><div class="c-rule-field c-rule-field-full-width svelte-1r8al3d"><!> <!></div></div>'),Le=C('<div class="l-file-controls svelte-1r8al3d" slot="header"><div class="l-file-controls-left svelte-1r8al3d"><div class="c-trigger-section svelte-1r8al3d"><!> <!> <!></div></div> <!></div>'),Be=C("<div>Loading...</div>"),De=C('<div class="c-rules-container svelte-1vbu0zh"><!></div>');ye(function(X,_){J(_,!1);const[j,q]=ge(),A=()=>$e(W,"$rule",j),G=new U(w),W=he(null),Y={handleMessageFromExtension(s){const t=s.data;if(t&&t.type===O.loadFile&&t){const h=t.data.content;if(h!==void 0){const a=h.replace(/^\n+/,""),o=S.getDescriptionFrontmatterKey(a),E=S.getRuleTypeFromContent(a),r=S.extractContent(a);W.set({path:t.data.pathName,content:r,type:E,description:o})}}return!0}};ve(()=>{G.registerConsumer(Y),w.postMessage({type:O.rulesLoaded})}),K();var H=De();me("message",fe,function(...s){var t;(t=G.onMessageFromExtension)==null||t.apply(this,s)});var Z=g(H),ee=s=>{(function(t,h){J(h,!1);const a=b(),o=b(),E=b();let r=ne(h,"rule",12),$=b(r().content),i=b(r().description);const I=new U(w),se=new Ee,ae=new Re(w,I,se),re=new Me(I),F=async(l,p)=>{r({...r(),type:l,description:p||e(i)}),p!==void 0&&f(i,p);try{await re.updateRuleContent({type:l,path:e(a),content:e($),description:p||e(i)})}catch(c){console.error("RulesMarkdownEditor: Error in rulesModel.updateRuleContent:",c)}},oe=xe.debounce(F,500),ie=()=>{w.postMessage({type:O.openSettingsPage,data:"guidelines"})};N(()=>L(r()),()=>{f(a,r().path)}),N(()=>L(r()),()=>{f(o,r().type)}),N(()=>(e(a),e(o),e($),e(i)),()=>{f(E,{path:e(a),type:e(o),content:e($),description:e(i)})}),ce(),K(),Ce(t,{saveFunction:()=>F(e(o),e(i)),variant:"surface",size:2,resize:"vertical",class:"markdown-editor",get value(){return e($)},set value(l){f($,l)},children:(l,p)=>{var c=de(),R=pe(c),T=u=>{var y=Ne(),k=g(y),z=g(k);B(z,{size:1,class:"c-field-label",children:(v,m)=>{var x=D("Description");n(v,x)},$$slots:{default:!0}});var d=M(z,2);ze(d,{placeholder:"When should this rules file be fetched by the Agent?",size:1,get value(){return e(i)},set value(v){f(i,v)},$$events:{input:()=>oe(e(o),e(i))},$$legacy:!0}),n(u,y)};P(R,u=>{e(o),L(V),ue(()=>e(o)===V.AGENT_REQUESTED)&&u(T)}),n(l,c)},$$slots:{default:!0,header:(l,p)=>{var c=Le(),R=g(c),T=g(R),u=g(T);ke(u,{content:"Navigate back to all Rules & Guidelines",children:(d,v)=>{be(d,{size:1,variant:"ghost-block",color:"neutral",class:"c-back-button",$$events:{click:ie},$$slots:{iconLeft:(m,x)=>{Te(m,{slot:"iconLeft"})}}})},$$slots:{default:!0}});var y=M(u,2);B(y,{size:1,class:"c-field-label",children:(d,v)=>{var m=D("Trigger:");n(d,m)},$$slots:{default:!0}});var k=M(y,2);Fe(k,{onSave:F,get rule(){return e(E)}});var z=M(R,2);we(z,{size:1,get path(){return e(a)},onOpenLocalFile:async()=>(ae.openFile({repoRoot:"",pathName:e(a)}),"success"),$$slots:{text:(d,v)=>{B(d,{slot:"text",size:1,children:(m,x)=>{var le=D("Open file");n(m,le)},$$slots:{default:!0}})}}}),n(l,c)}},$$legacy:!0}),Q()})(s,{get rule(){return A()}})},te=s=>{var t=Be();n(s,t)};P(Z,s=>{A()!==null?s(ee):s(te,!1)}),n(X,H),Q(),q()},{target:document.getElementById("app")});
