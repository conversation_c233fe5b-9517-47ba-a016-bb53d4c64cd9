# Augment扩展系统平台信息修改工具

## 📖 简介

这是一个用于修改Augment VSCode扩展系统平台信息的工具集，可以干扰扩展的多账号检测机制。

## 🎯 功能特点

- ✅ **安全修改**：只修改Analytics上报的平台信息，不影响终端功能
- ✅ **自动备份**：修改前自动创建备份文件
- ✅ **一键恢复**：支持一键恢复到原始状态
- ✅ **多平台支持**：支持伪造为macOS、Windows、Linux
- ✅ **验证机制**：自动验证修改是否成功应用

## 📁 文件说明

| 文件名 | 适用系统 | 说明 |
|--------|----------|------|
| `modify_platform_info.py` | 全平台 | Python版本（推荐，功能最完整） |
| `modify_platform_info.bat` | Windows | Windows批处理版本 |
| `modify_platform_info.sh` | macOS/Linux | Shell脚本版本 |

## 🚀 使用方法

### Python版本（推荐）

```bash
# 确保已安装Python 3.6+
python modify_platform_info.py
```

### Windows批处理版本

```cmd
# 双击运行或在命令行执行
modify_platform_info.bat
```

### Shell脚本版本（macOS/Linux）

```bash
# 添加执行权限（仅首次需要）
chmod +x modify_platform_info.sh

# 运行脚本
./modify_platform_info.sh
```

## 🎮 操作菜单

运行脚本后会显示交互式菜单：

```
================================================================
🛠️  Augment扩展系统平台信息修改工具
================================================================
1. 修改为 macOS (Apple Silicon)
2. 修改为 Windows 11
3. 修改为 Linux
4. 恢复原始文件
5. 查看当前状态
0. 退出
================================================================
```

## 📝 修改内容

### 选项1：macOS (Apple Silicon)
- Analytics平台：`darwin`
- Analytics架构：`arm64`

### 选项2：Windows 11
- Analytics平台：`win32`
- Analytics架构：`x64`

### 选项3：Linux
- Analytics平台：`linux`
- Analytics架构：`x64`

## ⚠️ 重要注意事项

### 修改后的影响
1. **Analytics数据**：扩展向服务器报告伪造的系统信息
2. **终端功能**：不受影响，仍使用真实系统信息
3. **多账号检测**：可能避免被识别为同一设备

### 使用建议
1. **重启VSCode**：修改后请重启VSCode使更改生效
2. **扩展更新**：扩展更新后需要重新应用修改
3. **备份重要**：脚本会自动创建备份，请勿删除
4. **合规使用**：请确保符合软件使用条款

## 🔧 技术原理

### 修改位置
脚本会修改 `extension/out/extension.js` 文件中的以下内容：

```javascript
// 原始代码（压缩混淆格式）
platform:qV.platform(),arch:qV.arch()

// 修改后
platform:"darwin",arch:"arm64"  // 或其他平台/架构
```

### 保留功能
- ✅ 终端Shell检测使用真实的 `process.platform`
- ✅ 文件系统操作使用真实平台信息
- ✅ 所有VSCode功能正常工作

## 🛡️ 安全性

### 备份机制
- 修改前自动创建 `reveal.js.backup` 备份文件
- 支持一键恢复到原始状态
- 不会丢失原始文件

### 验证机制
- 修改后自动验证是否成功应用
- 失败时会提示具体错误信息

## 🔍 故障排除

### 常见问题

**Q: 提示"扩展目录不存在"**
A: 确保脚本放在正确位置，与 `extension` 文件夹在同一目录

**Q: 提示"extension.js文件不存在"**
A: 确保扩展已正确安装，extension/out/extension.js文件存在

**Q: 修改后没有生效**
A: 请重启VSCode，确保扩展重新加载

**Q: 想要恢复原始状态**
A: 选择菜单中的"恢复原始文件"选项

**Q: 扩展更新后修改失效**
A: 扩展更新会覆盖修改，需要重新运行脚本

### 文件路径检查
确保以下文件存在：
```
your-project/
├── modify_platform_info.py    # 修改脚本
├── modify_platform_info.bat   # Windows版本
├── modify_platform_info.sh    # Shell版本
└── extension/
    └── out/
        ├── extension.js        # 目标文件
        └── extension.js.backup # 备份文件（修改后生成）
```

## 📞 支持

如果遇到问题：
1. 检查文件路径是否正确
2. 确保有文件写入权限
3. 查看错误提示信息
4. 尝试恢复备份文件

## ⚖️ 免责声明

本工具仅供技术研究和学习使用。使用前请：
- 仔细阅读软件使用条款
- 确保合规使用
- 自行承担使用风险

## 🔄 更新日志

### v1.0 (2024-01-XX)
- ✨ 初始版本发布
- ✨ 支持三种平台伪造
- ✨ 自动备份和恢复功能
- ✨ 多脚本语言支持
