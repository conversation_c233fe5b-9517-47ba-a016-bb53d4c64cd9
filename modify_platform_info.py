#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment扩展系统平台信息修改脚本
用于干扰扩展的系统平台信息检查，避免多账号检测

作者: AI Assistant
版本: 1.0
"""

import os
import sys
import shutil
import re
from datetime import datetime
from pathlib import Path

class PlatformModifier:
    def __init__(self):
        self.script_dir = Path(__file__).parent
        self.extension_dir = self.script_dir / "extension" / "out"
        self.extension_js = self.extension_dir / "extension.js"
        self.backup_file = self.extension_dir / "extension.js.backup"
        
        # 预定义的平台配置
        self.platforms = {
            "darwin": {
                "platform": "darwin",
                "arch": "arm64",
                "release": "23.1.0",
                "description": "macOS (Apple Silicon)"
            },
            "win32": {
                "platform": "win32",
                "arch": "x64",
                "release": "10.0.22621",
                "description": "Windows 11"
            },
            "linux": {
                "platform": "linux",
                "arch": "x64",
                "release": "5.15.0",
                "description": "Linux"
            }
        }
        
        # 修改点定义 - 针对压缩混淆的extension.js
        self.modifications = [
            {
                "name": "Analytics平台信息",
                "pattern": r'platform:\s*[a-zA-Z_$][a-zA-Z0-9_$]*\.platform\(\),\s*arch:\s*[a-zA-Z_$][a-zA-Z0-9_$]*\.arch\(\)',
                "template": 'platform:"{platform}",arch:"{arch}"'
            },
            {
                "name": "User-Agent平台信息",
                "pattern": r'`\$\{[^}]+\.platform\(\)\};\s*\$\{[^}]+\.arch\(\)\};\s*\$\{[^}]+\.release\(\)\}`',
                "template": '`{platform}; {arch}; {release}`'
            }
        ]

    def check_environment(self):
        """检查运行环境"""
        print("🔍 检查运行环境...")
        
        if not self.extension_dir.exists():
            print(f"❌ 错误: 扩展目录不存在: {self.extension_dir}")
            return False
            
        if not self.extension_js.exists():
            print(f"❌ 错误: extension.js文件不存在: {self.extension_js}")
            return False

        print(f"✅ 扩展目录: {self.extension_dir}")
        print(f"✅ 目标文件: {self.extension_js}")
        return True

    def create_backup(self):
        """创建备份文件"""
        print("\n📦 创建备份...")
        
        if self.backup_file.exists():
            print(f"⚠️  备份文件已存在: {self.backup_file}")
            choice = input("是否覆盖现有备份? (y/N): ").lower()
            if choice != 'y':
                print("❌ 取消操作")
                return False
        
        try:
            shutil.copy2(self.extension_js, self.backup_file)
            print(f"✅ 备份创建成功: {self.backup_file}")
            return True
        except Exception as e:
            print(f"❌ 备份创建失败: {e}")
            return False

    def restore_backup(self):
        """恢复备份文件"""
        print("\n🔄 恢复备份...")
        
        if not self.backup_file.exists():
            print(f"❌ 错误: 备份文件不存在: {self.backup_file}")
            return False
        
        try:
            shutil.copy2(self.backup_file, self.extension_js)
            print(f"✅ 恢复成功: {self.extension_js}")
            return True
        except Exception as e:
            print(f"❌ 恢复失败: {e}")
            return False

    def read_file(self):
        """读取文件内容"""
        try:
            with open(self.extension_js, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
            return None

    def write_file(self, content):
        """写入文件内容"""
        try:
            with open(self.extension_js, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except Exception as e:
            print(f"❌ 写入文件失败: {e}")
            return False

    def apply_modifications(self, platform_key):
        """应用修改"""
        print(f"\n🔧 应用修改 - {self.platforms[platform_key]['description']}...")

        content = self.read_file()
        if content is None:
            return False

        platform_config = self.platforms[platform_key]
        modified = False

        for mod in self.modifications:
            print(f"  📝 修改: {mod['name']}")

            # 使用正则表达式查找和替换
            pattern = mod['pattern']
            replacement = mod['template'].format(**platform_config)

            # 检查是否找到匹配
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                modified = True
                print(f"    ✅ 已修改: {mod['name']}")
            else:
                print(f"    ⚠️  未找到匹配模式: {mod['name']}")

        if modified:
            if self.write_file(content):
                print("✅ 所有修改已应用")
                return True
            else:
                return False
        else:
            print("❌ 没有应用任何修改")
            return False

    def verify_modifications(self, platform_key):
        """验证修改结果"""
        print("\n🔍 验证修改结果...")

        content = self.read_file()
        if content is None:
            return False

        platform_config = self.platforms[platform_key]
        success = True

        # 检查Analytics平台信息
        analytics_pattern = f'platform:"{platform_config["platform"]}",arch:"{platform_config["arch"]}"'
        if analytics_pattern in content:
            print("  ✅ Analytics平台信息已修改")
        else:
            print("  ❌ Analytics平台信息修改失败")
            success = False

        # 检查User-Agent信息（如果存在）
        user_agent_pattern = f'{platform_config["platform"]}; {platform_config["arch"]}; {platform_config["release"]}'
        if user_agent_pattern in content:
            print("  ✅ User-Agent平台信息已修改")
        else:
            print("  ⚠️  User-Agent平台信息未找到或未修改")

        return success

    def show_menu(self):
        """显示主菜单"""
        print("\n" + "="*60)
        print("🛠️  Augment扩展系统平台信息修改工具")
        print("="*60)
        print("1. 修改为 macOS (Apple Silicon)")
        print("2. 修改为 Windows 11") 
        print("3. 修改为 Linux")
        print("4. 恢复原始文件")
        print("5. 查看当前状态")
        print("0. 退出")
        print("="*60)

    def show_status(self):
        """显示当前状态"""
        print("\n📊 当前状态:")
        print(f"  扩展目录: {self.extension_dir}")
        print(f"  目标文件: {'✅ 存在' if self.extension_js.exists() else '❌ 不存在'}")
        print(f"  备份文件: {'✅ 存在' if self.backup_file.exists() else '❌ 不存在'}")
        
        if self.extension_js.exists():
            stat = self.extension_js.stat()
            mod_time = datetime.fromtimestamp(stat.st_mtime)
            print(f"  文件大小: {stat.st_size:,} 字节")
            print(f"  修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")

    def run(self):
        """运行主程序"""
        print("🚀 启动Augment扩展平台信息修改工具")
        
        if not self.check_environment():
            print("\n❌ 环境检查失败，程序退出")
            return
        
        while True:
            self.show_menu()
            choice = input("\n请选择操作 (0-5): ").strip()
            
            if choice == '0':
                print("\n👋 再见!")
                break
            elif choice == '1':
                self._modify_platform('darwin')
            elif choice == '2':
                self._modify_platform('win32')
            elif choice == '3':
                self._modify_platform('linux')
            elif choice == '4':
                self._restore_original()
            elif choice == '5':
                self.show_status()
            else:
                print("❌ 无效选择，请重试")
            
            input("\n按回车键继续...")

    def _modify_platform(self, platform_key):
        """修改平台信息的内部方法"""
        platform_info = self.platforms[platform_key]
        print(f"\n🎯 准备修改为: {platform_info['description']}")
        
        # 创建备份
        if not self.create_backup():
            return
        
        # 应用修改
        if self.apply_modifications(platform_key):
            # 验证修改
            if self.verify_modifications(platform_key):
                print(f"\n🎉 成功! 系统平台信息已修改为: {platform_info['description']}")
                print("\n📝 修改内容:")
                print(f"  • Analytics平台: {platform_info['platform']}")
                print(f"  • Analytics架构: {platform_info['arch']}")
                print("\n⚠️  注意事项:")
                print("  • 请重启VSCode以使修改生效")
                print("  • 扩展更新时需要重新应用修改")
                print("  • 终端功能使用真实系统信息，不受影响")
            else:
                print("\n❌ 修改验证失败，请检查文件内容")
        else:
            print("\n❌ 修改应用失败")

    def _restore_original(self):
        """恢复原始文件的内部方法"""
        if self.restore_backup():
            print("\n🎉 成功! 已恢复为原始文件")
            print("\n📝 恢复内容:")
            print("  • 所有修改已撤销")
            print("  • 系统平台信息恢复为真实值")
            print("\n⚠️  注意: 请重启VSCode以使恢复生效")
        else:
            print("\n❌ 恢复失败")


if __name__ == "__main__":
    try:
        modifier = PlatformModifier()
        modifier.run()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
