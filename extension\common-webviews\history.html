<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment - History</title>
    <script nonce="nonce-t1xh928eyKussz3A6/5A4w==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <script type="module" crossorigin src="./assets/history-8AltY5jJ.js" nonce="nonce-t1xh928eyKussz3A6/5A4w=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-kH3m-zOb.js" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-IgXUmngh.js" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-C8Qb_O9b.js" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-BAmr_-U4.js" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-BZfc2Zk1.js" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-TeF8u93x.js" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-IsRYkfgU.js" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="modulepreload" crossorigin href="./assets/CopyButton-BDqSrh0t.js" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="modulepreload" crossorigin href="./assets/differenceInCalendarDays-CwIWhZCr.js" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="modulepreload" crossorigin href="./assets/index-D-fDrvnq.js" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="modulepreload" crossorigin href="./assets/index-B_qLJJaQ.js" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-Cb9MCs64.js" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="modulepreload" crossorigin href="./assets/next-edit-types-904A5ehg.js" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-Cs9y5rDt.js" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B4afvB2A.css" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-DRIZURB3.css" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-BcSV_kHI.css" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-CEzLOEg8.css" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-k8sG2hbx.css" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="stylesheet" crossorigin href="./assets/CopyButton-B0_wR17F.css" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="stylesheet" crossorigin href="./assets/index-BlHvDt2c.css" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
    <link rel="stylesheet" crossorigin href="./assets/history-Cwf2a8EN.css" nonce="nonce-t1xh928eyKussz3A6/5A4w==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
